{"cells": [{"cell_type": "markdown", "id": "e79b46b6", "metadata": {}, "source": ["Load dataset"]}, {"cell_type": "code", "execution_count": 1, "id": "b0ee57c5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3276 entries, 0 to 3275\n", "Data columns (total 10 columns):\n", " #   Column           Non-Null Count  Dtype  \n", "---  ------           --------------  -----  \n", " 0   ph               2785 non-null   float64\n", " 1   Hardness         3276 non-null   float64\n", " 2   Solids           3276 non-null   float64\n", " 3   Chloramines      3276 non-null   float64\n", " 4   Sulfate          2495 non-null   float64\n", " 5   Conductivity     3276 non-null   float64\n", " 6   Organic_carbon   3276 non-null   float64\n", " 7   Trihalomethanes  3114 non-null   float64\n", " 8   Turbidity        3276 non-null   float64\n", " 9   Potability       3276 non-null   int64  \n", "dtypes: float64(9), int64(1)\n", "memory usage: 256.1 KB\n", "None\n", "\n", "Missing values:\n", " ph                 491\n", "Hardness             0\n", "Solids               0\n", "Chloramines          0\n", "Sulfate            781\n", "Conductivity         0\n", "Organic_carbon       0\n", "Trihalomethanes    162\n", "Turbidity            0\n", "Potability           0\n", "dtype: int64\n", "\n", "Preview:\n", "          ph    Hardness        Solids  Chloramines     Sulfate  Conductivity  \\\n", "0       NaN  204.890455  20791.318981     7.300212  368.516441    564.308654   \n", "1  3.716080  129.422921  18630.057858     6.635246         NaN    592.885359   \n", "2  8.099124  224.236259  19909.541732     9.275884         NaN    418.606213   \n", "3  8.316766  214.373394  22018.417441     8.059332  356.886136    363.266516   \n", "4  9.092223  181.101509  17978.986339     6.546600  310.135738    398.410813   \n", "\n", "   Organic_carbon  Trihalomethanes  Turbidity  Potability  \n", "0       10.379783        86.990970   2.963135           0  \n", "1       15.180013        56.329076   4.500656           0  \n", "2       16.868637        66.420093   3.055934           0  \n", "3       18.436524       100.341674   4.628771           0  \n", "4       11.558279        31.997993   4.075075           0  \n"]}], "source": ["import pandas as pd\n", "\n", "df = pd.read_csv(\"water_potability.csv\")\n", "\n", "# Display basic info\n", "print(df.info())\n", "print(\"\\nMissing values:\\n\", df.isnull().sum())\n", "print(\"\\nPreview:\\n\", df.head())\n"]}, {"cell_type": "markdown", "id": "9a8fad39", "metadata": {}, "source": ["Median Imputation (Baseline)"]}, {"cell_type": "code", "execution_count": 2, "id": "d150b59b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ph                 0\n", "Hardness           0\n", "Solids             0\n", "Chloramines        0\n", "Sulfate            0\n", "Conductivity       0\n", "Organic_carbon     0\n", "Trihalomethanes    0\n", "Turbidity          0\n", "Potability         0\n", "dtype: int64\n"]}], "source": ["df_clean = df.copy()\n", "\n", "# Median imputation for missing values\n", "df_clean.fillna(df_clean.median(numeric_only=True), inplace=True)\n", "\n", "# Confirm that missing values are gone\n", "print(df_clean.isnull().sum())\n"]}, {"cell_type": "markdown", "id": "5d79613a", "metadata": {}, "source": ["Outlier Detection"]}, {"cell_type": "code", "execution_count": 3, "id": "c855a1e1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                ph     Hardness        Solids  Chloramines      Sulfate  \\\n", "count  3276.000000  3276.000000   3276.000000  3276.000000  3276.000000   \n", "mean      7.074194   196.369496  22014.092526     7.122277   333.608364   \n", "std       1.470040    32.879761   8768.570828     1.583085    36.143851   \n", "min       0.000000    47.432000    320.942611     0.352000   129.000000   \n", "25%       6.277673   176.850538  15666.690297     6.127421   317.094638   \n", "50%       7.036752   196.967627  20927.833607     7.130299   333.073546   \n", "75%       7.870050   216.667456  27332.762127     8.114887   350.385756   \n", "max      14.000000   323.124000  61227.196008    13.127000   481.030642   \n", "\n", "       Conductivity  Organic_carbon  Trihalomethanes    Turbidity   Potability  \n", "count   3276.000000     3276.000000      3276.000000  3276.000000  3276.000000  \n", "mean     426.205111       14.284970        66.407478     3.966786     0.390110  \n", "std       80.824064        3.308162        15.769958     0.780382     0.487849  \n", "min      181.483754        2.200000         0.738000     1.450000     0.000000  \n", "25%      365.734414       12.065801        56.647656     3.439711     0.000000  \n", "50%      421.884968       14.218338        66.622485     3.955028     0.000000  \n", "75%      481.792304       16.557652        76.666609     4.500320     1.000000  \n", "max      753.342620       28.300000       124.000000     6.739000     1.000000  \n", "\n", "Unusual pH values:\n", "            ph  Potability\n", "80    1.844538           0\n", "104   2.612036           0\n", "354   2.798549           1\n", "692   1.757037           1\n", "726   0.227499           1\n", "810   0.989912           1\n", "1231  2.690831           0\n", "1343  2.569244           0\n", "2165  2.803563           0\n", "2189  2.558103           0\n", "2300  2.974429           1\n", "2343  2.538116           1\n", "2473  2.945469           0\n", "2681  2.376768           0\n", "2899  1.431782           0\n", "2928  0.975578           0\n", "2932  2.925174           0\n", "3014  0.000000           0\n", "3088  2.128531           0\n", "3094  1.985383           0\n"]}], "source": ["# See min/max values for each column\n", "print(df_clean.describe())\n", "\n", "# Specifically inspect edge cases in pH\n", "print(\"\\nUnusual pH values:\")\n", "print(df_clean[df_clean[\"ph\"] < 3][[\"ph\", \"Potability\"]])  # ph < 3 is usually not potable\n"]}, {"cell_type": "markdown", "id": "afe72ec0", "metadata": {}, "source": [" Clip Extreme pH Values"]}, {"cell_type": "markdown", "id": "7b176061", "metadata": {}, "source": ["To reflect practical field conditions and embedded calibration thresholds, we applied value clipping to pH readings below 3.0, which are chemically implausible and likely result from low-cost sensor error."]}, {"cell_type": "code", "execution_count": 4, "id": "dfb154c7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extreme pH values before clip: 20\n", "Extreme pH values after clip: 0\n", "New min and max pH: 3.0 13.999999999999998\n"]}], "source": ["# Count rows where pH is below 3 before clipping\n", "print(\"Extreme pH values before clip:\", (df_clean[\"ph\"] < 3).sum())\n", "\n", "# Clip pH values to a reasonable sensor-safe range\n", "df_clean[\"ph\"] = df_clean[\"ph\"].clip(lower=3.0, upper=14.0)\n", "\n", "# Confirm after clipping\n", "print(\"Extreme pH values after clip:\", (df_clean[\"ph\"] < 3).sum())\n", "print(\"New min and max pH:\", df_clean[\"ph\"].min(), df_clean[\"ph\"].max())\n"]}, {"cell_type": "markdown", "id": "45b0c090", "metadata": {}, "source": ["Scaling / Normalization Check"]}, {"cell_type": "code", "execution_count": 5, "id": "14bb83fd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                ph     Hardness        Solids  Chloramines      Sulfate  \\\n", "count  3276.000000  3276.000000   3276.000000  3276.000000  3276.000000   \n", "mean      7.079953   196.369496  22014.092526     7.122277   333.608364   \n", "std       1.450477    32.879761   8768.570828     1.583085    36.143851   \n", "min       3.000000    47.432000    320.942611     0.352000   129.000000   \n", "25%       6.277673   176.850538  15666.690297     6.127421   317.094638   \n", "50%       7.036752   196.967627  20927.833607     7.130299   333.073546   \n", "75%       7.870050   216.667456  27332.762127     8.114887   350.385756   \n", "max      14.000000   323.124000  61227.196008    13.127000   481.030642   \n", "\n", "       Conductivity  Organic_carbon  Trihalomethanes    Turbidity   Potability  \n", "count   3276.000000     3276.000000      3276.000000  3276.000000  3276.000000  \n", "mean     426.205111       14.284970        66.407478     3.966786     0.390110  \n", "std       80.824064        3.308162        15.769958     0.780382     0.487849  \n", "min      181.483754        2.200000         0.738000     1.450000     0.000000  \n", "25%      365.734414       12.065801        56.647656     3.439711     0.000000  \n", "50%      421.884968       14.218338        66.622485     3.955028     0.000000  \n", "75%      481.792304       16.557652        76.666609     4.500320     1.000000  \n", "max      753.342620       28.300000       124.000000     6.739000     1.000000  \n"]}], "source": ["# See min, max, and standard deviation per column\n", "print(df_clean.describe())\n"]}, {"cell_type": "markdown", "id": "31925941", "metadata": {}, "source": ["Distance-based models (like KNN, NGBoost, quantile forests) need scaling so well make a df scaled version"]}, {"cell_type": "code", "execution_count": 6, "id": "f7b311a1", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "ph", "rawType": "float64", "type": "float"}, {"name": "Hardness", "rawType": "float64", "type": "float"}, {"name": "Solids", "rawType": "float64", "type": "float"}, {"name": "Chloramines", "rawType": "float64", "type": "float"}, {"name": "Sulfate", "rawType": "float64", "type": "float"}, {"name": "Conductivity", "rawType": "float64", "type": "float"}, {"name": "Organic_carbon", "rawType": "float64", "type": "float"}, {"name": "Trihalomethanes", "rawType": "float64", "type": "float"}, {"name": "Turbidity", "rawType": "float64", "type": "float"}, {"name": "Potability", "rawType": "int64", "type": "integer"}], "ref": "6f57bbb9-abdf-4507-b41a-a911ff099951", "rows": [["0", "-0.029788354408036795", "0.25919471072588524", "-0.1394708713751093", "0.11241484558326897", "0.9659569987660399", "1.708954233030653", "-1.1806505657521278", "1.3054337277832433", "-1.2862975839507313", "0"], ["1", "-2.3195040595155763", "-2.0364136650369358", "-0.385986649553349", "-0.3076937081044106", "-0.014799206837658466", "2.062574999579364", "0.27059723987304235", "-0.6391862837121229", "0.6842178911400506", "0"], ["2", "0.7027531395782498", "0.8476648329496834", "-0.2400473367970158", "1.3605938595071565", "-0.014799206837658466", "-0.09403211476423624", "0.7811168574507188", "0.0008000125863123948", "-1.1673654621705243", "0"], ["3", "0.8528244512486047", "0.5476513738775178", "0.0004933044420881189", "0.5920078206187585", "0.64412961134175", "-0.7788299619632442", "1.2551344298168203", "2.1521539541519092", "0.8484115203631912", "0"], ["4", "1.3875286816625507", "-0.46442908670804023", "-0.46024857027889415", "-0.36369793312202997", "-0.6495215502874558", "-0.34393890863273974", "-0.824357169283179", "-2.182297472909101", "0.1387855311835096", "0"]], "shape": {"columns": 10, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ph</th>\n", "      <th>Hardness</th>\n", "      <th>Solids</th>\n", "      <th>Chloramines</th>\n", "      <th>Sulfate</th>\n", "      <th>Conductivity</th>\n", "      <th>Organic_carbon</th>\n", "      <th>Trihalomethanes</th>\n", "      <th>Turbidity</th>\n", "      <th>Potability</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-0.029788</td>\n", "      <td>0.259195</td>\n", "      <td>-0.139471</td>\n", "      <td>0.112415</td>\n", "      <td>0.965957</td>\n", "      <td>1.708954</td>\n", "      <td>-1.180651</td>\n", "      <td>1.305434</td>\n", "      <td>-1.286298</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-2.319504</td>\n", "      <td>-2.036414</td>\n", "      <td>-0.385987</td>\n", "      <td>-0.307694</td>\n", "      <td>-0.014799</td>\n", "      <td>2.062575</td>\n", "      <td>0.270597</td>\n", "      <td>-0.639186</td>\n", "      <td>0.684218</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.702753</td>\n", "      <td>0.847665</td>\n", "      <td>-0.240047</td>\n", "      <td>1.360594</td>\n", "      <td>-0.014799</td>\n", "      <td>-0.094032</td>\n", "      <td>0.781117</td>\n", "      <td>0.000800</td>\n", "      <td>-1.167365</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.852824</td>\n", "      <td>0.547651</td>\n", "      <td>0.000493</td>\n", "      <td>0.592008</td>\n", "      <td>0.644130</td>\n", "      <td>-0.778830</td>\n", "      <td>1.255134</td>\n", "      <td>2.152154</td>\n", "      <td>0.848412</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.387529</td>\n", "      <td>-0.464429</td>\n", "      <td>-0.460249</td>\n", "      <td>-0.363698</td>\n", "      <td>-0.649522</td>\n", "      <td>-0.343939</td>\n", "      <td>-0.824357</td>\n", "      <td>-2.182297</td>\n", "      <td>0.138786</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         ph  Hardness    Solids  Chloramines   Sulfate  Conductivity  \\\n", "0 -0.029788  0.259195 -0.139471     0.112415  0.965957      1.708954   \n", "1 -2.319504 -2.036414 -0.385987    -0.307694 -0.014799      2.062575   \n", "2  0.702753  0.847665 -0.240047     1.360594 -0.014799     -0.094032   \n", "3  0.852824  0.547651  0.000493     0.592008  0.644130     -0.778830   \n", "4  1.387529 -0.464429 -0.460249    -0.363698 -0.649522     -0.343939   \n", "\n", "   Organic_carbon  Trihalomethanes  Turbidity  Potability  \n", "0       -1.180651         1.305434  -1.286298           0  \n", "1        0.270597        -0.639186   0.684218           0  \n", "2        0.781117         0.000800  -1.167365           0  \n", "3        1.255134         2.152154   0.848412           0  \n", "4       -0.824357        -2.182297   0.138786           0  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.preprocessing import StandardScaler\n", "\n", "# Create scaler and scale everything except Potability\n", "scaler = StandardScaler()\n", "scaled_features = scaler.fit_transform(df_clean.drop(\"Potability\", axis=1))\n", "\n", "# Reassemble into a DataFrame\n", "df_scaled = pd.DataFrame(scaled_features, columns=df_clean.columns[:-1])\n", "df_scaled[\"Potability\"] = df_clean[\"Potability\"].values\n", "\n", "# Show first few rows of scaled data\n", "df_scaled.head()\n"]}, {"cell_type": "markdown", "id": "f4973245", "metadata": {}, "source": ["#Train Random Forest Classifier (Baseline)"]}, {"cell_type": "code", "execution_count": 7, "id": "2c2afe5d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Random Forest Performance:\n", "Accuracy: 0.6494\n", "F1 Score: 0.3915\n", "ROC AUC: 0.6423\n", "\n", "Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.66      0.88      0.75       400\n", "           1       0.61      0.29      0.39       256\n", "\n", "    accuracy                           0.65       656\n", "   macro avg       0.63      0.58      0.57       656\n", "weighted avg       0.64      0.65      0.61       656\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 128300 (\\N{MICROSCOPE}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA6sAAAHWCAYAAACRwR+zAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAABmr0lEQVR4nO3deVgV5f//8dcR5ICs7oghuOCO+5KioqWhlmlZlpmKa+aWpVaWmlta7ntWlpiZVmpq5r4vuSsuicpHJc1wFxBNQJjfH/04X4+gAoKc5Pm4rnPFueeee94zczBf3nNmTIZhGAIAAAAAwIbkyu4CAAAAAAC4F2EVAAAAAGBzCKsAAAAAAJtDWAUAAAAA2BzCKgAAAADA5hBWAQAAAAA2h7AKAAAAALA5hFUAAAAAgM0hrAIAAAAAbA5hFQAA4D8gODhYvr6+2V0GADw2hFUAQKoiIiJkMplSfT399NNZss2///5bw4YNU2hoaJaM/yiSj8f48eOzu5QMW7lypYYNG5bdZdi8kJAQq8+7vb29ihYtquDgYJ0/fz67y7MZ9x6nu18ffvhhdpeXqtGjR2vp0qXZXQaANLLP7gIAAFnjjz/+UNWqVeXg4JDq8vj4eIWFhalkyZIPHKdt27Zq3ry5VVvBggUzrc67/f333xo+fLh8fX1VpUqVLNlGTrZy5UrNmDGDwJpGI0aMUPHixXX79m3t2rVLISEh2r59u44ePSpHR8fsLs9mJB+nu1WsWDGbqnmw0aNH65VXXlGrVq2yuxQAaUBYBYAnlGEYqlWrlrZv357q8qefflqGYTx0nGrVqunNN9/M7PIeq9u3b8vBwUG5cuXMC4pu3rwpZ2fn7C7jP6dZs2aqUaOGJKlr164qUKCAPv/8cy1fvlxt2rTJ5upsx93HKTPxuQWQM/+vDQDINMePH9crr7yifPnyydHRUTVq1NDy5cut+ly7dk0DBgyQv7+/XFxc5ObmpmbNmunQoUOWPps3b1bNmjUlSZ06dbJcThgSEiJJ8vX1VXBwcIrtN2zYUA0bNrQax2QyaeHChRo8eLCKFi2qPHnyKCYmRpK0e/duNW3aVO7u7sqTJ48CAwO1Y8eODO178mWQ27dvV9++fVWwYEF5eHjorbfeUnx8vKKiotShQwflzZtXefPm1fvvv2/1DwR3X1o8adIk+fj4yMnJSYGBgTp69GiK7W3cuFH169eXs7OzPDw81LJlS4WFhVn1GTZsmEwmk44dO6Y33nhDefPmVb169RQcHKwZM2ZIktXlmsnGjx+vunXrKn/+/HJyclL16tW1aNGiFDWYTCb17t1bS5cuVcWKFWU2m1WhQgWtXr06Rd/z58+rS5cu8vLyktlsVvHixfX2228rPj7e0icqKkr9+vWTt7e3zGazSpUqpc8//1xJSUlWYy1cuFDVq1eXq6ur3Nzc5O/vrylTpqTxTGWO+vXrS5JOnTplaYuPj9fQoUNVvXp1ubu7y9nZWfXr19emTZus1r37XH/11VcqWbKkzGazatasqb1796bYVvLxdXR0VMWKFfXLL7+kWtPNmzfVv39/y/ErU6aMxo8fn+IfopLP288//6zy5cvLyclJderU0ZEjRyRJX375pUqVKiVHR0c1bNhQERERj3KorDzK5zbZ999/r+rVq8vJyUn58uXT66+/rnPnzlmNER4ertatW8vT01OOjo566qmn9Prrrys6OtpyDG7evKm5c+daPv+p/ZkCwHYwswoAeKBbt27pypUrVm3u7u7KnTu3/vjjDwUEBKho0aL68MMP5ezsrJ9++kmtWrXS4sWL9dJLL0mSTp8+raVLl+rVV19V8eLFdfHiRX355ZcKDAzUsWPH5OXlpXLlymnEiBEaOnSounfvbgkGdevWzVDdI0eOlIODgwYMGKC4uDg5ODho48aNatasmapXr65PPvlEuXLl0pw5c/TMM89o27ZtqlWrVoa21adPH3l6emr48OHatWuXvvrqK3l4eOj3339XsWLFNHr0aK1cuVLjxo1TxYoV1aFDB6v1v/vuO924cUO9evXS7du3NWXKFD3zzDM6cuSIChcuLElav369mjVrphIlSmjYsGH6559/NG3aNAUEBOjAgQMpbrzz6quvys/PT6NHj5ZhGKpatar+/vtvrVu3TvPmzUuxD1OmTNGLL76odu3aKT4+XgsXLtSrr76qFStW6Pnnn7fqu337di1ZskQ9e/aUq6urpk6dqtatW+vs2bPKnz+/pH8v6a5Vq5aioqLUvXt3lS1bVufPn9eiRYt069YtOTg46NatWwoMDNT58+f11ltvqVixYvr99981aNAgRUZGavLkyZKkdevWqW3btnr22Wf1+eefS5LCwsK0Y8cOvfPOOxk6ZxmRHODy5s1raYuJidHs2bPVtm1bdevWTTdu3NA333yjoKAg7dmzJ8Xl7D/88INu3Liht956SyaTSWPHjtXLL7+s06dPK3fu3JKktWvXqnXr1ipfvrzGjBmjq1evqlOnTnrqqaesxjIMQy+++KI2bdqkLl26qEqVKlqzZo0GDhyo8+fPa9KkSVb9t23bpuXLl6tXr16SpDFjxuiFF17Q+++/r5kzZ6pnz566fv26xo4dq86dO2vjxo1pOi7R0dEp/owoUKCApEf/3ErSp59+qiFDhqhNmzbq2rWrLl++rGnTpqlBgwY6ePCgPDw8FB8fr6CgIMXFxVl+H8+fP68VK1YoKipK7u7umjdvnrp27apatWqpe/fukvTQr0EAyGYGAOCJdOTIESMgIOC+y2vXrm2Eh4ffd/mZM2cMSam+Nm3aZBiGYTz77LOGv7+/cfv2bct6SUlJRt26dQ0/Pz9L2+3bt43ExMQU45vNZmPEiBGWtr179xqSjDlz5qSox8fHx+jYsWOK9sDAQCMwMNDyftOmTYYko0SJEsatW7es6vLz8zOCgoKMpKQkS/utW7eM4sWLG02aNLnvsbj7eIwbN87SNmfOHENSijHr1KljmEwmo0ePHpa2O3fuGE899ZRVrcljOjk5GX/99Zelfffu3YYk491337W0ValSxShUqJBx9epVS9uhQ4eMXLlyGR06dLC0ffLJJ4Yko23btin2oVevXsb9/td/97EyDMOIj483KlasaDzzzDNW7ZIMBwcH43//+59VHZKMadOmWdo6dOhg5MqVy9i7d2+KbSUfq5EjRxrOzs7GyZMnrZZ/+OGHhp2dnXH27FnDMAzjnXfeMdzc3Iw7d+6kWntmSz6v69evNy5fvmycO3fOWLRokVGwYEHDbDYb586ds/S9c+eOERcXZ7X+9evXjcKFCxudO3e2tCWf6/z58xvXrl2ztC9btsyQZPz666+WtipVqhhFihQxoqKiLG1r1641JBk+Pj6WtqVLlxqSjFGjRllt/5VXXjFMJpPVOZJkmM1m48yZM5a2L7/80pBkeHp6GjExMZb2QYMGGZKs+j7oOKX2untfHuVzGxERYdjZ2RmffvqpVfuRI0cMe3t7S/vBgwcNScbPP//8wJqdnZ1T/XMEgG3iMmAAwAN1795d69ats3pVrlxZ165d08aNG9WmTRvduHFDV65c0ZUrV3T16lUFBQUpPDzccudUs9ls+b5oYmKirl69KhcXF5UpU0YHDhzIkro7duwoJycny/vQ0FCFh4frjTfe0NWrVy313rx5U88++6y2bt2a4tLTtOrSpYvVJbW1a9eWYRjq0qWLpc3Ozk41atTQ6dOnU6zfqlUrFS1a1PK+Vq1aql27tlauXClJioyMVGhoqIKDg5UvXz5Lv0qVKqlJkyaWfnfr0aNHuvbh7mN1/fp1RUdHq379+qmen8aNG1vNSFWqVElubm6WfUtKStLSpUvVokWLVL/LmHysfv75Z9WvX1958+a1nI8rV66ocePGSkxM1NatWyVJHh4eunnzptatW5eufXpUjRs3VsGCBeXt7a1XXnlFzs7OWr58udUMp52dneUmZklJSbp27Zru3LmjGjVqpHrsXnvtNauZ2eQrCJKPXfK57tixo9zd3S39mjRpovLly1uNtXLlStnZ2alv375W7f3795dhGFq1apVV+7PPPms1k1m7dm1JUuvWreXq6pqiPbXPampmzJiR4s+Iu/flUT63S5YsUVJSktq0aWP1GfH09JSfn5/lcuvkY7VmzRrdunUrTXUDsH1cBgwAeCA/Pz81btw4RfuePXtkGIaGDBmiIUOGpLrupUuXVLRoUSUlJWnKlCmaOXOmzpw5o8TEREuf5MtGM9u9dycNDw+X9G+IvZ/o6GirIJFWxYoVs3qf/Bdnb2/vFO3Xr19Psb6fn1+KttKlS+unn36SJP3555+SpDJlyqToV65cOa1ZsybFzWju3f+HWbFihUaNGqXQ0FDFxcVZ2u8O4cnu3V/p30tjk/ft8uXLiomJeegdYcPDw3X48OH73l360qVLkqSePXvqp59+UrNmzVS0aFE999xzatOmjZo2bfrA8S9fvmz1WUtmZ2eXpjtaz5gxQ6VLl1Z0dLS+/fZbbd26VWazOUW/uXPnasKECTp+/LgSEhIs7amdg3uPXfLnLfnYJZ/r1D4T9/7jzp9//ikvLy+roCn9+5m4e6z7bftBn9O7a3qYWrVqpfqPEpnxuQ0PD5dhGKkeD0mWS6eLFy+u9957TxMnTtT8+fNVv359vfjii3rzzTetQj+A/xbCKgAgQ5JnIQcMGKCgoKBU+5QqVUrSv4+LGDJkiDp37qyRI0cqX758ypUrl/r165fm2czUQpP070ytnZ1diva7ZwrvrnfcuHH3fSyOi4tLmmq5V2rbv1+7kYY7MGeGe/f/QbZt26YXX3xRDRo00MyZM1WkSBHlzp1bc+bM0Q8//JCi//32N737lpSUpCZNmuj9999PdXnp0qUlSYUKFVJoaKjWrFmjVatWadWqVZozZ446dOiguXPn3nf8mjVrpghskuTj45OmGwjdHcJatWqlevXq6Y033tCJEycsn5Xvv/9ewcHBatWqlQYOHKhChQrJzs5OY8aMsboRU7LMOnYZkZ7PqfT4Pqt3S+331mQyadWqVanWeffv7IQJExQcHKxly5Zp7dq16tu3r8aMGaNdu3al+L4vgP8GwioAIENKlCgh6d+ZjdRmXu+2aNEiNWrUSN98841Ve1RUlOVGLNL9A6n07wxUVFRUivY///zTUsuDJF+26ubm9tB6H7fkWd+7nTx50nLJpo+PjyTpxIkTKfodP35cBQoUSNMjPu53fBcvXixHR0etWbPGauZwzpw5aSk/hYIFC8rNzS3VOxrfrWTJkoqNjU3T+XBwcFCLFi3UokULJSUlqWfPnvryyy81ZMgQyz+K3Gv+/Pn6559/UrSnJ8gnSw6gjRo10vTp0/Xhhx9K+vezXaJECS1ZssTq+H7yySfp3ob0f+c6tc/Eveffx8dH69ev140bN6xmV48fP241VnbJjM9tyZIlZRiGihcvbvnHiwfx9/eXv7+/Bg8erN9//10BAQGaNWuWRo0aJenBf8YAsD18ZxUAkCGFChVSw4YN9eWXXyoyMjLF8suXL1t+trOzSzFL8/PPP1u+05os+S+uqYXSkiVLateuXVaPPVmxYkWKx1fcT/Xq1VWyZEmNHz9esbGxD6z3cVu6dKnVsdizZ492796tZs2aSZKKFCmiKlWqaO7cuVbH5ujRo1q7dq2aN2+epu3c7/ja2dnJZDJZXTIbERGhpUuXZmh/cuXKpVatWunXX3/Vvn37UixP/iy0adNGO3fu1Jo1a1L0iYqK0p07dyRJV69eTTF+pUqVJMnqkuV7BQQEqHHjxileAQEBGdqvhg0bqlatWpo8ebJu374t6f9mJe/+fO/evVs7d+7M0DbuPtfJj1yR/r0j8rFjx6z6Nm/eXImJiZo+fbpV+6RJk2QymSyfn+ySGZ/bl19+WXZ2dho+fHiKP0MMw7B8NmJiYiyfl2T+/v7KlSuX1WfE2dk51T9fANgmZlYBABk2Y8YM1atXT/7+/urWrZtKlCihixcvaufOnfrrr78sz1F94YUXNGLECHXq1El169bVkSNHNH/+/BQzoiVLlpSHh4dmzZolV1dXOTs7q3bt2ipevLi6du2qRYsWqWnTpmrTpo1OnTql77//Ps2PnsiVK5dmz56tZs2aqUKFCurUqZOKFi2q8+fPa9OmTXJzc9Ovv/6a6ccoLUqVKqV69erp7bffVlxcnCZPnqz8+fNbXR47btw4NWvWTHXq1FGXLl0sjwBxd3fXsGHD0rSd6tWrS5L69u2roKAg2dnZ6fXXX9fzzz+viRMnqmnTpnrjjTd06dIlzZgxQ6VKldLhw4cztE+jR4/W2rVrFRgYqO7du6tcuXKKjIzUzz//rO3bt8vDw0MDBw7U8uXL9cILLyg4OFjVq1fXzZs3deTIES1atEgREREqUKCAunbtqmvXrumZZ57RU089pT///FPTpk1TlSpVLN/PfFwGDhyoV199VSEhIerRo4deeOEFLVmyRC+99JKef/55nTlzRrNmzVL58uVT/UeRtBgzZoyef/551atXT507d9a1a9c0bdo0VahQwWrMFi1aqFGjRvr4448VERGhypUra+3atVq2bJn69etnE49ledTPbcmSJTVq1CgNGjRIERERatWqlVxdXXXmzBn98ssv6t69uwYMGKCNGzeqd+/eevXVV1W6dGnduXNH8+bNk52dnVq3bm0Zr3r16lq/fr0mTpwoLy8vFS9e3HJDKQA2KDtuQQwAyHqZ9eiaux/VkppTp04ZHTp0MDw9PY3cuXMbRYsWNV544QVj0aJFlj63b982+vfvbxQpUsRwcnIyAgICjJ07d6Z47Ixh/Psoj/Llyxv29vYpHmMzYcIEo2jRoobZbDYCAgKMffv23ffRNfd7hMXBgweNl19+2cifP79hNpsNHx8fo02bNsaGDRseuJ8PenTNvY9nSX4Mx+XLl63aO3bsaDg7O6c65oQJEwxvb2/DbDYb9evXNw4dOpSihvXr1xsBAQGGk5OT4ebmZrRo0cI4duxYmrZtGP8+ZqVPnz5GwYIFDZPJZPWIkW+++cbw8/MzzGazUbZsWWPOnDmWse4myejVq1eKsVN7tNCff/5pdOjQwfLIlxIlShi9evWyetTLjRs3jEGDBhmlSpUyHBwcjAIFChh169Y1xo8fb8THxxuGYRiLFi0ynnvuOaNQoUKGg4ODUaxYMeOtt94yIiMjU9SRGe53Xg3DMBITE42SJUsaJUuWNO7cuWMkJSUZo0ePNnx8fAyz2WxUrVrVWLFihdGxY0erx8w86PdJkvHJJ59YtS1evNgoV66cYTabjfLlyxtLlixJMaZh/Hv83n33XcPLy8vInTu34efnZ4wbN87qUUrJ27j3vN2vpof9DqXlON3tUT+3hvHv8ahXr57h7OxsODs7G2XLljV69eplnDhxwjAMwzh9+rTRuXNno2TJkoajo6ORL18+o1GjRsb69eutxjl+/LjRoEEDw8nJyZDEY2wAG2cyjGz49jwAIMsdPXpUPXr00Pbt21Nd/vTTT+v777+/7/f9kPUiIiJUvHhxjRs3TgMGDMjucgAAsCl8ZxUAAAAAYHP4zioAPMF27dolDw+PVJdl9Pt0AAAAjwNhFQCeUBUrVkxxd0wAAID/Cr6zCgAAAACwOXxnFQAAAABgcwirAAAAAACbw3dWkeWSkpL0999/y9XVVSaTKbvLAQAAAJBNDMPQjRs35OXlpVy5Hjx3SlhFlvv777/l7e2d3WUAAAAAsBHnzp3TU0899cA+hFVkOVdXV0n/fiDd3NyyuRoAAAAA2SUmJkbe3t6WjPAghFVkueRLf93c3AirAAAAANL09UBusAQAAAAAsDnMrOKxaTB4gezMTtldBgAAAJBj7B/XIbtLyDBmVgEAAAAANoewCgAAAACwOYRVAAAAAIDNIawCAAAAAGwOYRUAAAAAYHMIqwAAAAAAm0NYBQAAAADYHMIqAAAAAMDmEFYBAAAAADaHsAoAAAAAsDmEVQAAAACAzSGs4oF8fX01efLk7C4DAAAAQA5DWAUAAAAA2BzCKgAAAADA5hBWc7iGDRuqd+/e6t27t9zd3VWgQAENGTJEhmFY+ty6dUudO3eWq6urihUrpq+++iobKwYAAACQExBWoblz58re3l579uzRlClTNHHiRM2ePduyfMKECapRo4YOHjyonj176u2339aJEyfuO15cXJxiYmKsXgAAAACQHoRVyNvbW5MmTVKZMmXUrl079enTR5MmTbIsb968uXr27KlSpUrpgw8+UIECBbRp06b7jjdmzBi5u7tbXt7e3o9jNwAAAAA8QQir0NNPPy2TyWR5X6dOHYWHhysxMVGSVKlSJcsyk8kkT09PXbp06b7jDRo0SNHR0ZbXuXPnsq54AAAAAE8k++wuALYvd+7cVu9NJpOSkpLu299sNstsNmd1WQAAAACeYMysQrt377Z6v2vXLvn5+cnOzi6bKgIAAACQ0xFWobNnz+q9997TiRMntGDBAk2bNk3vvPNOdpcFAAAAIAfjMmCoQ4cO+ueff1SrVi3Z2dnpnXfeUffu3bO7LAAAAAA5GGEVyp07tyZPnqwvvvgixbKIiIgUbaGhoVlfFAAAAIAcjcuAAQAAAAA2h7AKAAAAALA5XAacw23evDm7SwAAAACAFJhZBQAAAADYHMIqAAAAAMDmEFYBAAAAADaHsAoAAAAAsDmEVQAAAACAzSGsAgAAAABsDmEVAAAAAGBzeM4qHputo9rKzc0tu8sAAAAA8B/AzCoAAAAAwOYQVgEAAAAANoewCgAAAACwOYRVAAAAAIDNIawCAAAAAGwOYRUAAAAAYHMIqwAAAAAAm0NYBQAAAADYHPvsLgA5R4PBC2RndsruMgAAAIAn0v5xHbK7hEzFzCoAAAAAwOYQVgEAAAAANoewCgAAAACwOYRVAAAAAIDNIawCAAAAAGwOYRUAAAAAYHMIqwAAAAAAm0NYBQAAAADYHMIqAAAAAMDmEFYBAAAAADaHsAoAAAAAsDmE1SfE5s2bZTKZFBUVZWlbunSpSpUqJTs7O/Xr1y/bagMAAACA9CKs2ojLly/r7bffVrFixWQ2m+Xp6amgoCDt2LEjw2O+9dZbeuWVV3Tu3DmNHDkyTes0bNiQYAsAAAAg29lndwH4V+vWrRUfH6+5c+eqRIkSunjxojZs2KCrV69maLzY2FhdunRJQUFB8vLyyuRqAQAAACBrMbNqA6KiorRt2zZ9/vnnatSokXx8fFSrVi0NGjRIL774oiIiImQymRQaGmq1jslk0ubNm1OMt3nzZrm6ukqSnnnmGUu/q1evqm3btipatKjy5Mkjf39/LViwwLJecHCwtmzZoilTpshkMslkMikiIkKSdPToUTVr1kwuLi4qXLiw2rdvrytXrmTlYQEAAACQgxFWbYCLi4tcXFy0dOlSxcXFPfJ4devW1YkTJyRJixcvVmRkpOrWravbt2+revXq+u2333T06FF1795d7du31549eyRJU6ZMUZ06ddStWzdFRkYqMjJS3t7eioqK0jPPPKOqVatq3759Wr16tS5evKg2bdqkuv24uDjFxMRYvQAAAAAgPQirNsDe3l4hISGaO3euPDw8FBAQoI8++kiHDx/O0HgODg4qVKiQJClfvnzy9PSUg4ODihYtqgEDBqhKlSoqUaKE+vTpo6ZNm+qnn36SJLm7u8vBwUF58uSRp6enPD09ZWdnp+nTp6tq1aoaPXq0ypYtq6pVq+rbb7/Vpk2bdPLkyRTbHzNmjNzd3S0vb2/vjB8cAAAAADkSYdVGtG7dWn///beWL1+upk2bavPmzapWrZpCQkIybRuJiYkaOXKk/P39lS9fPrm4uGjNmjU6e/bsA9c7dOiQNm3aZJkBdnFxUdmyZSVJp06dStF/0KBBio6OtrzOnTuXafsAAAAAIGfgBks2xNHRUU2aNFGTJk00ZMgQde3aVZ988om2bdsmSTIMw9I3ISEh3eOPGzdOU6ZM0eTJk+Xv7y9nZ2f169dP8fHxD1wvNjZWLVq00Oeff55iWZEiRVK0mc1mmc3mdNcHAAAAAMkIqzasfPnyWrp0qQoWLChJioyMVNWqVSXJ6mZLabVjxw61bNlSb775piQpKSlJJ0+eVPny5S19HBwclJiYaLVetWrVtHjxYvn6+sreno8MAAAAgKzHZcA24OrVq3rmmWf0/fff6/Dhwzpz5ox+/vlnjR07Vi1btpSTk5OefvppffbZZwoLC9OWLVs0ePDgdG/Hz89P69at0++//66wsDC99dZbunjxolUfX19f7d69WxEREbpy5YqSkpLUq1cvXbt2TW3bttXevXt16tQprVmzRp06dUoRbAEAAAAgMxBWbYCLi4tq166tSZMmqUGDBqpYsaKGDBmibt26afr06ZKkb7/9Vnfu3FH16tXVr18/jRo1Kt3bGTx4sKpVq6agoCA1bNhQnp6eatWqlVWfAQMGyM7OTuXLl1fBggV19uxZeXl5aceOHUpMTNRzzz0nf39/9evXTx4eHsqVi48QAAAAgMxnMu7+IiSQBWJiYuTu7q7KfWbJzuyU3eUAAAAAT6T94zpkdwkPlZwNoqOj5ebm9sC+TIsBAAAAAGwOYRUAAAAAYHMIqwAAAAAAm0NYBQAAAADYHMIqAAAAAMDmEFYBAAAAADaHsAoAAAAAsDmEVQAAAACAzSGsAgAAAABsDmEVAAAAAGBz7LO7AOQcW0e1lZubW3aXAQAAAOA/gJlVAAAAAIDNIawCAAAAAGwOYRUAAAAAYHMIqwAAAAAAm0NYBQAAAADYHMIqAAAAAMDmEFYBAAAAADaHsAoAAAAAsDn22V0Aco4GgxfIzuyU3WUAAAAAD7V/XIfsLiHHY2YVAAAAAGBzCKsAAAAAAJtDWAUAAAAA2BzCKgAAAADA5hBWAQAAAAA2h7AKAAAAALA5hFUAAAAAgM0hrAIAAAAAbA5hFQAAAABgcwirAAAAAACbQ1gFAAAAANgcwup/0ObNm2UymRQVFZXdpQAAAABAliCsZoLg4GC1atUqRTuhEgAAAAAyhrBqw+Lj47O7BAAAAADIFoTVx+Tq1atq27atihYtqjx58sjf318LFiyw6tOwYUP17t1b/fr1U4ECBRQUFCRJWrlypUqXLi0nJyc1atRIERERVuuFhITIw8NDa9asUbly5eTi4qKmTZsqMjLSqt/s2bNVrlw5OTo6qmzZspo5c6ZlWXx8vHr37q0iRYrI0dFRPj4+GjNmjCTJMAwNGzZMxYoVk9lslpeXl/r27ZsFRwkAAAAA/mWf3QXkFLdv31b16tX1wQcfyM3NTb/99pvat2+vkiVLqlatWpZ+c+fO1dtvv60dO3ZIks6dO6eXX35ZvXr1Uvfu3bVv3z71798/xfi3bt3S+PHjNW/ePOXKlUtvvvmmBgwYoPnz50uS5s+fr6FDh2r69OmqWrWqDh48qG7dusnZ2VkdO3bU1KlTtXz5cv30008qVqyYzp07p3PnzkmSFi9erEmTJmnhwoWqUKGCLly4oEOHDt13X+Pi4hQXF2d5HxMTkynHEAAAAEDOQVjNJCtWrJCLi4tVW2JiouXnokWLasCAAZb3ffr00Zo1a/TTTz9ZhVU/Pz+NHTvW8v6jjz5SyZIlNWHCBElSmTJldOTIEX3++edW20pISNCsWbNUsmRJSVLv3r01YsQIy/JPPvlEEyZM0MsvvyxJKl68uI4dO6Yvv/xSHTt21NmzZ+Xn56d69erJZDLJx8fHsu7Zs2fl6empxo0bK3fu3CpWrJhVzfcaM2aMhg8f/vCDBgAAAAD3wWXAmaRRo0YKDQ21es2ePduyPDExUSNHjpS/v7/y5csnFxcXrVmzRmfPnrUap3r16lbvw8LCVLt2bau2OnXqpNh+njx5LEFVkooUKaJLly5Jkm7evKlTp06pS5cucnFxsbxGjRqlU6dOSfr3JlGhoaEqU6aM+vbtq7Vr11rGevXVV/XPP/+oRIkS6tatm3755RfduXPnvsdi0KBBio6OtrySZ2gBAAAAIK2YWc0kzs7OKlWqlFXbX3/9Zfl53LhxmjJliiZPnix/f385OzurX79+KW6i5OzsnKHt586d2+q9yWSSYRiSpNjYWEnS119/nSL42tnZSZKqVaumM2fOaNWqVVq/fr3atGmjxo0ba9GiRfL29taJEye0fv16rVu3Tj179tS4ceO0ZcuWFNuVJLPZLLPZnKH9AAAAAACJsPrY7NixQy1bttSbb74pSUpKStLJkydVvnz5B65Xrlw5LV++3Kpt165d6dp24cKF5eXlpdOnT6tdu3b37efm5qbXXntNr732ml555RU1bdpU165dU758+eTk5KQWLVqoRYsW6tWrl8qWLasjR46oWrVq6aoFAAAAANKCsPqY+Pn5adGiRfr999+VN29eTZw4URcvXnxoWO3Ro4cmTJiggQMHqmvXrtq/f79CQkLSvf3hw4erb9++cnd3V9OmTRUXF6d9+/bp+vXreu+99zRx4kQVKVJEVatWVa5cufTzzz/L09NTHh4eCgkJUWJiomrXrq08efLo+++/l5OTk9X3WgEAAAAgM/Gd1cdk8ODBqlatmoKCgtSwYUN5enqqVatWD12vWLFiWrx4sZYuXarKlStr1qxZGj16dLq337VrV82ePVtz5syRv7+/AgMDFRISouLFi0uSXF1dNXbsWNWoUUM1a9ZURESEVq5cqVy5csnDw0Nff/21AgICVKlSJa1fv16//vqr8ufPn+46AAAAACAtTEbyFxuBLBITEyN3d3dV7jNLdman7C4HAAAAeKj94zpkdwlPpORsEB0dLTc3twf2ZWYVAAAAAGBzCKsAAAAAAJtDWAUAAAAA2BzCKgAAAADA5hBWAQAAAAA2h7AKAAAAALA5hFUAAAAAgM0hrAIAAAAAbA5hFQAAAABgcwirAAAAAACbY5/dBSDn2Dqqrdzc3LK7DAAAAAD/AcysAgAAAABsDmEVAAAAAGBzCKsAAAAAAJtDWAUAAAAA2BzCKgAAAADA5hBWAQAAAAA2h7AKAAAAALA5PGcVj02DwQtkZ3bK7jIAAADwhNo/rkN2l4BMxMwqAAAAAMDmEFYBAAAAADaHsAoAAAAAsDmEVQAAAACAzSGsAgAAAABsDmEVAAAAAGBzCKsAAAAAAJtDWAUAAAAA2BzCKgAAAADA5hBWAQAAAAA2h7AKAAAAALA5hFUAAAAAgM0hrN7DZDJp6dKl912+efNmmUwmRUVFPbaa0uth+wAAAAAAti7HhdULFy6oT58+KlGihMxms7y9vdWiRQtt2LAhu0vLNJGRkWrWrFl2lwEAAAAAGWaf3QU8ThEREQoICJCHh4fGjRsnf39/JSQkaM2aNerVq5eOHz/+WOqIj4+Xg4NDlo3v6emZZWMDAAAAwOOQo2ZWe/bsKZPJpD179qh169YqXbq0KlSooPfee0+7du2y9Lty5Ypeeukl5cmTR35+flq+fPkDx128eLEqVKggs9ksX19fTZgwwWq5r6+vRo4cqQ4dOsjNzU3du3eXJH3wwQcqXbq08uTJoxIlSmjIkCFKSEiwrDds2DBVqVJF3377rYoVKyYXFxf17NlTiYmJGjt2rDw9PVWoUCF9+umnVtu7+zLgiIgImUwmLVmyRI0aNVKePHlUuXJl7dy502qd7du3q379+nJycpK3t7f69u2rmzdvWpbPnDlTfn5+cnR0VOHChfXKK6/c93jExcUpJibG6gUAAAAA6ZFjwuq1a9e0evVq9erVS87OzimWe3h4WH4ePny42rRpo8OHD6t58+Zq166drl27luq4+/fvV5s2bfT666/ryJEjGjZsmIYMGaKQkBCrfuPHj1flypV18OBBDRkyRJLk6uqqkJAQHTt2TFOmTNHXX3+tSZMmWa136tQprVq1SqtXr9aCBQv0zTff6Pnnn9dff/2lLVu26PPPP9fgwYO1e/fuB+7/xx9/rAEDBig0NFSlS5dW27ZtdefOHcs2mjZtqtatW+vw4cP68ccftX37dvXu3VuStG/fPvXt21cjRozQiRMntHr1ajVo0OC+2xozZozc3d0tL29v7wfWBgAAAAD3MhmGYWR3EY/Dnj17VLt2bS1ZskQvvfTSffuZTCYNHjxYI0eOlCTdvHlTLi4uWrVqlZo2barNmzerUaNGun79ujw8PNSuXTtdvnxZa9eutYzx/vvv67ffftMff/wh6d+Z1apVq+qXX355YI3jx4/XwoULtW/fPkn/zqyOGzdOFy5ckKurqySpadOmOnHihE6dOqVcuf79t4ayZcsqODhYH374oWUffvnlF7Vq1UoREREqXry4Zs+erS5dukiSjh07pgoVKigsLExly5ZV165dZWdnpy+//NJSy/bt2xUYGKibN29q5cqV6tSpk/766y9LHQ8SFxenuLg4y/uYmBh5e3urcp9ZsjM7PXR9AAAAICP2j+uQ3SXgIWJiYuTu7q7o6Gi5ubk9sG+O+c5qejJ5pUqVLD87OzvLzc1Nly5dSrVvWFiYWrZsadUWEBCgyZMnKzExUXZ2dpKkGjVqpFj3xx9/1NSpU3Xq1CnFxsbqzp07KU6Yr6+vVUAsXLiw7OzsLEE1ue1+9aW2T0WKFJEkXbp0SWXLltWhQ4d0+PBhzZ8/39LHMAwlJSXpzJkzatKkiXx8fFSiRAk1bdpUTZs2tVwmnRqz2Syz2fzAegAAAADgQXLMZcB+fn4ymUxpuolS7ty5rd6bTCYlJSU90vbvvfR4586dateunZo3b64VK1bo4MGD+vjjjxUfH//QWjJS393rmEwmSbKsExsbq7feekuhoaGW16FDhxQeHq6SJUvK1dVVBw4c0IIFC1SkSBENHTpUlStXtunH9wAAAAD4b8sxM6v58uVTUFCQZsyYob59+6YIj1FRUVbfW02rcuXKaceOHVZtO3bsUOnSpS2zqqn5/fff5ePjo48//tjS9ueff6Z7+5mhWrVqOnbsmEqVKnXfPvb29mrcuLEaN26sTz75RB4eHtq4caNefvnlx1gpAAAAgJwix8ysStKMGTOUmJioWrVqafHixQoPD1dYWJimTp2qOnXqZGjM/v37a8OGDRo5cqROnjypuXPnavr06RowYMAD1/Pz89PZs2e1cOFCnTp1SlOnTn3od1qzygcffKDff/9dvXv3VmhoqMLDw7Vs2TLLDZZWrFihqVOnKjQ0VH/++ae+++47JSUlqUyZMtlSLwAAAIAnX44KqyVKlNCBAwfUqFEj9e/fXxUrVlSTJk20YcMGffHFFxkas1q1avrpp5+0cOFCVaxYUUOHDtWIESMUHBz8wPVefPFFvfvuu+rdu7eqVKmi33//3XKX4MetUqVK2rJli06ePKn69euratWqGjp0qLy8vCT9e6fkJUuW6JlnnlG5cuU0a9YsLViwQBUqVMiWegEAAAA8+XLM3YCRfZLv+MXdgAEAAJCVuBuw7UvP3YBz1MwqAAAAAOC/gbAKAAAAALA5hFUAAAAAgM0hrAIAAAAAbA5hFQAAAABgcwirAAAAAACbQ1gFAAAAANgcwioAAAAAwOYQVgEAAAAANoewCgAAAACwOfbZXQByjq2j2srNzS27ywAAAADwH8DMKgAAAADA5hBWAQAAAAA2J8Nhdd68eQoICJCXl5f+/PNPSdLkyZO1bNmyTCsOAAAAAJAzZSisfvHFF3rvvffUvHlzRUVFKTExUZLk4eGhyZMnZ2Z9AAAAAIAcKENhddq0afr666/18ccfy87OztJeo0YNHTlyJNOKAwAAAADkTBkKq2fOnFHVqlVTtJvNZt28efORiwIAAAAA5GwZCqvFixdXaGhoivbVq1erXLlyj1oTAAAAACCHy9BzVt977z316tVLt2/flmEY2rNnjxYsWKAxY8Zo9uzZmV0jnhANBi+Qndkpu8sAAABAFto/rkN2l4AnRIbCateuXeXk5KTBgwfr1q1beuONN+Tl5aUpU6bo9ddfz+waAQAAAAA5TLrD6p07d/TDDz8oKChI7dq1061btxQbG6tChQplRX0AAAAAgBwo3d9Ztbe3V48ePXT79m1JUp48eQiqAAAAAIBMlaEbLNWqVUsHDx7M7FoAAAAAAJCUwe+s9uzZU/3799dff/2l6tWry9nZ2Wp5pUqVMqU4AAAAAEDOlKGwmnwTpb59+1raTCaTDMOQyWRSYmJi5lQHAAAAAMiRMhRWz5w5k9l1AAAAAABgkaGw6uPjk9l1AAAAAABgkaGw+t133z1weYcOPAgYAAAAAJBxGQqr77zzjtX7hIQE3bp1Sw4ODsqTJw9hFQAAAADwSDL06Jrr169bvWJjY3XixAnVq1dPCxYsyOwaAQAAAAA5TIbCamr8/Pz02WefpZh1hW0YNmyYqlSpYnkfHBysVq1aPXCdhg0bql+/fllaFwAAAACkJtPCqiTZ29vr77//zswh8f9dvnxZb7/9tooVKyaz2SxPT08FBQVpx44dGRpvypQpCgkJydwiAQAAACCTZOg7q8uXL7d6bxiGIiMjNX36dAUEBGRKYbDWunVrxcfHa+7cuSpRooQuXryoDRs26OrVqxkaz93dPZMrBAAAAIDMk6Gweu/loyaTSQULFtQzzzyjCRMmZEZduEtUVJS2bdumzZs3KzAwUNK/jw+qVauWpc/Zs2fVp08fbdiwQbly5VLTpk01bdo0FS5cONUxg4ODFRUVpaVLl0qSbt68qbfffltLliyRq6urBgwYkGKdmTNnatKkSTp37pzc3d1Vv359LVq0KPN3GAAAAECOl6GwmpSUlNl14AFcXFzk4uKipUuX6umnn5bZbLZanpSUpJYtW8rFxUVbtmzRnTt31KtXL7322mvavHlzmrYxcOBAbdmyRcuWLVOhQoX00Ucf6cCBA5bvue7bt099+/bVvHnzVLduXV27dk3btm1Lday4uDjFxcVZ3sfExGRovwEAAADkXBn6zuqIESN069atFO3//POPRowY8chFwZq9vb1CQkI0d+5ceXh4KCAgQB999JEOHz4sSdqwYYOOHDmiH374QdWrV1ft2rX13XffacuWLdq7d+9Dx4+NjdU333yj8ePH69lnn5W/v7/mzp2rO3fuWPqcPXtWzs7OeuGFF+Tj46OqVauqb9++qY43ZswYubu7W17e3t6ZcyAAAAAA5BgZCqvDhw9XbGxsivZbt25p+PDhj1wUUmrdurX+/vtvLV++XE2bNtXmzZtVrVo1hYSEKCwsTN7e3lahsHz58vLw8FBYWNhDxz516pTi4+NVu3ZtS1u+fPlUpkwZy/smTZrIx8dHJUqUUPv27TV//vxU/8FCkgYNGqTo6GjL69y5c4+w5wAAAAByogyFVcMwZDKZUrQfOnRI+fLle+SikDpHR0c1adJEQ4YM0e+//67g4GB98sknj2Xbrq6uOnDggBYsWKAiRYpo6NChqly5sqKiolL0NZvNcnNzs3oBAAAAQHqkK6zmzZtX+fLlk8lkUunSpZUvXz7Ly93dXU2aNFGbNm2yqlbco3z58rp586bKlSunc+fOWc1gHjt2TFFRUSpfvvxDxylZsqRy586t3bt3W9quX7+ukydPWvWzt7dX48aNNXbsWB0+fFgRERHauHFj5u0QAAAAAPx/6brB0uTJk2UYhjp37qzhw4dbPf7EwcFBvr6+qlOnTqYXmdNdvXpVr776qjp37qxKlSrJ1dVV+/bt09ixY9WyZUs1btxY/v7+ateunSZPnqw7d+6oZ8+eCgwMVI0aNR46vouLi7p06aKBAwcqf/78KlSokD7++GPlyvV//5axYsUKnT59Wg0aNFDevHm1cuVKJSUlWV0qDAAAAACZJV1htWPHjpKk4sWLq27dusqdO3eWFAVrLi4uql27tiZNmqRTp04pISFB3t7e6tatmz766COZTCYtW7ZMffr0UYMGDaweXZNW48aNU2xsrFq0aCFXV1f1799f0dHRluUeHh5asmSJhg0bptu3b8vPz08LFixQhQoVsmKXAQAAAORwJsMwjEcZ4Pbt24qPj7dq4zuKuFtMTIzc3d1Vuc8s2ZmdsrscAAAAZKH94zpkdwmwYcnZIDo6+qG5MUM3WLp165Z69+6tQoUKydnZWXnz5rV6AQAAAADwKDIUVgcOHKiNGzfqiy++kNls1uzZszV8+HB5eXnpu+++y+waAQAAAAA5TLq+s5rs119/1XfffaeGDRuqU6dOql+/vkqVKiUfHx/Nnz9f7dq1y+w6AQAAAAA5SIZmVq9du6YSJUpI+vf7qdeuXZMk1atXT1u3bs286gAAAAAAOVKGwmqJEiV05swZSVLZsmX1008/Sfp3xtXDwyPTigMAAAAA5EwZCqudOnXSoUOHJEkffvihZsyYIUdHR7377rsaOHBgphYIAAAAAMh5MvSd1Xfffdfyc+PGjXX8+HHt379fpUqVUqVKlTKtOAAAAABAzpShsHq327dvy8fHRz4+PplRDwAAAAAAGbsMODExUSNHjlTRokXl4uKi06dPS5KGDBmib775JlMLBAAAAADkPBkKq59++qlCQkI0duxYOTg4WNorVqyo2bNnZ1pxAAAAAICcyWQYhpHelUqVKqUvv/xSzz77rFxdXXXo0CGVKFFCx48fV506dXT9+vWsqBX/UTExMXJ3d1d0dLTc3NyyuxwAAAAA2SQ92SBDM6vnz59XqVKlUrQnJSUpISEhI0MCAAAAAGCRobBavnx5bdu2LUX7okWLVLVq1UcuCgAAAACQs2XobsBDhw5Vx44ddf78eSUlJWnJkiU6ceKEvvvuO61YsSKzawQAAAAA5DDpmlk9ffq0DMNQy5Yt9euvv2r9+vVydnbW0KFDFRYWpl9//VVNmjTJqloBAAAAADlEumZW/fz8FBkZqUKFCql+/frKly+fjhw5osKFC2dVfQAAAACAHChdM6v33jh41apVunnzZqYWBAAAAABAhm6wlCwDT70BAAAAAOCh0hVWTSaTTCZTijYAAAAAADJTur6zahiGgoODZTabJUm3b99Wjx495OzsbNVvyZIlmVchnhgNBi+Qndkpu8sAAADINvvHdcjuEoD/jHSF1Y4dO1q9f/PNNzO1GAAAAAAApHSG1Tlz5mRVHQAAAAAAWDzSDZYAAAAAAMgKhFUAAAAAgM0hrAIAAAAAbA5hFQAAAABgcwirAAAAAACbQ1gFAAAAANgcwioAAAAAwOYQVgEAAAAANoewCgAAAACwOYTVx6Rhw4bq169flm/HZDJp6dKlae7v6+uryZMnZ1k9AAAAAJAROS6sXrhwQX369FGJEiVkNpvl7e2tFi1aaMOGDdldWroMGzZMVapUSdEeGRmpZs2apXmcvXv3qnv37pb36Q27AAAAAJAV7LO7gMcpIiJCAQEB8vDw0Lhx4+Tv76+EhAStWbNGvXr10vHjx7O7xEfm6emZrv4FCxbMokoAAAAAIONy1Mxqz549ZTKZtGfPHrVu3VqlS5dWhQoV9N5772nXrl2SpLNnz6ply5ZycXGRm5ub2rRpo4sXL1rGSJ7RnDdvnnx9feXu7q7XX39dN27csPS5efOmOnToIBcXFxUpUkQTJkxIUUtqM5geHh4KCQmxvP/rr7/Utm1b5cuXT87OzqpRo4Z2796tkJAQDR8+XIcOHZLJZJLJZLKsd/e4devW1QcffGC1jcuXLyt37tzaunWrJOvLgH19fSVJL730kkwmk3x9fRUREaFcuXJp3759VuNMnjxZPj4+SkpKStOxBwAAAID0yDFh9dq1a1q9erV69eolZ2fnFMs9PDyUlJSkli1b6tq1a9qyZYvWrVun06dP67XXXrPqe+rUKS1dulQrVqzQihUrtGXLFn322WeW5QMHDtSWLVu0bNkyrV27Vps3b9aBAwfSVW9sbKwCAwN1/vx5LV++XIcOHdL777+vpKQkvfbaa+rfv78qVKigyMhIRUZGpqhRktq1a6eFCxfKMAxL248//igvLy/Vr18/Rf+9e/dKkubMmaPIyEjt3btXvr6+aty4sebMmWPVd86cOQoODlauXCk/QnFxcYqJibF6AQAAAEB65JjLgP/3v//JMAyVLVv2vn02bNigI0eO6MyZM/L29pYkfffdd6pQoYL27t2rmjVrSpKSkpIUEhIiV1dXSVL79u21YcMGffrpp4qNjdU333yj77//Xs8++6wkae7cuXrqqafSVe8PP/ygy5cva+/evcqXL58kqVSpUpblLi4usre3f+Blv23atFG/fv20fft2Szj94Ycf1LZtW5lMphT9ky8J9vDwsBq3a9eu6tGjhyZOnCiz2awDBw7oyJEjWrZsWarbHTNmjIYPH56u/QUAAACAu+WYmdW7ZxfvJywsTN7e3pagKknly5eXh4eHwsLCLG2+vr6WoCpJRYoU0aVLlyT9O+saHx+v2rVrW5bny5dPZcqUSVe9oaGhqlq1qiWoZkTBggX13HPPaf78+ZKkM2fOaOfOnWrXrl26xmnVqpXs7Oz0yy+/SJJCQkLUqFEjy2XD9xo0aJCio6Mtr3PnzmV4HwAAAADkTDkmrPr5+clkMmXKTZRy585t9d5kMqX7u5smkylFgE5ISLD87OTklPEC79KuXTstWrRICQkJ+uGHH+Tv7y9/f/90jeHg4KAOHTpozpw5io+P1w8//KDOnTvft7/ZbJabm5vVCwAAAADSI8eE1Xz58ikoKEgzZszQzZs3UyyPiopSuXLldO7cOauZwGPHjikqKkrly5dP03ZKliyp3Llza/fu3Za269ev6+TJk1b9ChYsqMjISMv78PBw3bp1y/K+UqVKCg0N1bVr11LdjoODgxITEx9aT8uWLXX79m2tXr1aP/zww0NnVXPnzp3quF27dtX69es1c+ZM3blzRy+//PJDtw0AAAAAGZVjwqokzZgxQ4mJiapVq5YWL16s8PBwhYWFaerUqapTp44aN24sf39/tWvXTgcOHNCePXvUoUMHBQYGqkaNGmnahouLi7p06aKBAwdq48aNOnr0aKo3InrmmWc0ffp0HTx4UPv27VOPHj2sZmzbtm0rT09PtWrVSjt27NDp06e1ePFi7dy5U9K/lyKfOXNGoaGhunLliuLi4lKtx9nZWa1atdKQIUMUFhamtm3bPrB+X19fbdiwQRcuXND169ct7eXKldPTTz+tDz74QG3bts20mV8AAAAASE2OCqslSpTQgQMH1KhRI/Xv318VK1ZUkyZNtGHDBn3xxRcymUxatmyZ8ubNqwYNGqhx48YqUaKEfvzxx3RtZ9y4capfv75atGihxo0bq169eqpevbpVnwkTJsjb21v169fXG2+8oQEDBihPnjyW5Q4ODlq7dq0KFSqk5s2by9/fX5999pns7OwkSa1bt1bTpk3VqFEjFSxYUAsWLLhvPe3atdOhQ4dUv359FStW7IG1T5gwQevWrZO3t7eqVq1qtaxLly6Kj49/4CXAAAAAAJAZTEZa7jwESBo5cqR+/vlnHT58OF3rxcTEyN3dXZX7zJKdmRlZAACQc+0f1yG7SwCyVXI2iI6Ofui9bXLUzCoyJjY2VkePHtX06dPVp0+f7C4HAAAAQA5AWMVD9e7dW9WrV1fDhg25BBgAAADAY2Gf3QXA9oWEhCgkJCS7ywAAAACQgzCzCgAAAACwOYRVAAAAAIDNIawCAAAAAGwOYRUAAAAAYHMIqwAAAAAAm0NYBQAAAADYHMIqAAAAAMDm8JxVPDZbR7WVm5tbdpcBAAAA4D+AmVUAAAAAgM0hrAIAAAAAbA5hFQAAAABgcwirAAAAAACbQ1gFAAAAANgcwioAAAAAwOYQVgEAAAAANoewCgAAAACwOfbZXQByjgaDF8jO7JTdZQAAAGSa/eM6ZHcJwBOLmVUAAAAAgM0hrAIAAAAAbA5hFQAAAABgcwirAAAAAACbQ1gFAAAAANgcwioAAAAAwOYQVgEAAAAANoewCgAAAACwOYRVAAAAAIDNIawCAAAAAGwOYRUAAAAAYHMIq/8hvr6+mjx58gP7mEwmLV269L7LIyIiZDKZFBoaKknavHmzTCaToqKiMq1OAAAAAHhUhNUsYDKZHvgaNmxYlm07MjJSzZo1S3P/unXrKjIyUu7u7pKkkJAQeXh4ZFF1AAAAAJA29tldwJMoMjLS8vOPP/6ooUOH6sSJE5Y2FxeXdI0XHx8vBweHNPX19PRM19gODg7pXgcAAAAAshozq1nA09PT8nJ3d5fJZLK8nzVrlurVq2fVf/LkyfL19bW8Dw4OVqtWrfTpp5/Ky8tLZcqUsSy7ceOG2rZtK2dnZxUtWlQzZsywGuvey4D37NmjqlWrytHRUTVq1NDBgwet+t99GfDmzZvVqVMnRUdHW80CjxgxQhUrVkyxn1WqVNGQIUMe4UgBAAAAQOoIqzZqw4YNOnHihNatW6cVK1ZY2seNG6fKlSvr4MGD+vDDD/XOO+9o3bp1qY4RGxurF154QeXLl9f+/fs1bNgwDRgw4L7brFu3riZPniw3NzdFRkYqMjJSAwYMUOfOnRUWFqa9e/da+h48eFCHDx9Wp06dUowTFxenmJgYqxcAAAAApAeXAdsoZ2dnzZ49O8XlvwEBAfrwww8lSaVLl9aOHTs0adIkNWnSJMUYP/zwg5KSkvTNN9/I0dFRFSpU0F9//aW333471W06ODhYzQQnc3FxUVBQkObMmaOaNWtKkubMmaPAwECVKFEixThjxozR8OHDM7zvAAAAAMDMqo3y9/dP9XuqderUSfE+LCws1THCwsJUqVIlOTo63nf9tOrWrZsWLFig27dvKz4+Xj/88IM6d+6cat9BgwYpOjra8jp37lyGtgkAAAAg52Jm9THLlSuXDMOwaktISEjRz9nZ+XGVlCYtWrSQ2WzWL7/8IgcHByUkJOiVV15Jta/ZbJbZbH7MFQIAAAB4khBWH7OCBQvqwoULMgxDJpNJkizPPE2LXbt2pXhfrly5VPuWK1dO8+bN0+3bty2zq/eufy8HBwclJiamaLe3t1fHjh01Z84cOTg46PXXX5eTk1Oa6wYAAACA9OAy4MesYcOGunz5ssaOHatTp05pxowZWrVqVZrX37Fjh8aOHauTJ09qxowZ+vnnn/XOO++k2veNN96QyWRSt27ddOzYMa1cuVLjx49/4Pi+vr6KjY3Vhg0bdOXKFd26dcuyrGvXrtq4caNWr15930uAAQAAACAzEFYfs3LlymnmzJmaMWOGKleurD179jzwDr336t+/v/bt26eqVatq1KhRmjhxooKCglLt6+Liol9//VVHjhxR1apV9fHHH+vzzz9/4Ph169ZVjx499Nprr6lgwYIaO3asZZmfn5/q1q2rsmXLqnbt2mmuGQAAAADSy2Tc+wVK4D4Mw5Cfn5969uyp9957L83rxcTEyN3dXZX7zJKdmUuHAQDAk2P/uA7ZXQLwn5KcDaKjo+Xm5vbAvnxnFWly+fJlLVy4UBcuXEj12aoAAAAAkJkIq0iTQoUKqUCBAvrqq6+UN2/e7C4HAAAAwBOOsIo04WpxAAAAAI8TN1gCAAAAANgcwioAAAAAwOYQVgEAAAAANoewCgAAAACwOYRVAAAAAIDNIawCAAAAAGwOYRUAAAAAYHN4zioem62j2srNzS27ywAAAADwH8DMKgAAAADA5hBWAQAAAAA2h7AKAAAAALA5hFUAAAAAgM0hrAIAAAAAbA5hFQAAAABgcwirAAAAAACbw3NW8dg0GLxAdman7C4DAADgofaP65DdJQA5HjOrAAAAAACbQ1gFAAAAANgcwioAAAAAwOYQVgEAAAAANoewCgAAAACwOYRVAAAAAIDNIawCAAAAAGwOYRUAAAAAYHMIqwAAAAAAm0NYBQAAAADYHMIqAAAAAMDmEFYBAAAAADaHsJpBERERMplMCg0Nze5S7is4OFitWrXK7jIAAAAAIN1sNqyeO3dOnTt3lpeXlxwcHOTj46N33nlHV69eze7SJEne3t6KjIxUxYoVs7sUAAAAAHji2GRYPX36tGrUqKHw8HAtWLBA//vf/zRr1ixt2LBBderU0bVr11JdLz4+/rHVaGdnJ09PT9nb2z+2baZVYmKikpKSsrsMAAAAAMgwmwyrvXr1koODg9auXavAwEAVK1ZMzZo10/r163X+/Hl9/PHHkiRfX1+NHDlSHTp0kJubm7p37y5J+vrrr+Xt7a08efLopZde0sSJE+Xh4WEZ/9SpU2rZsqUKFy4sFxcX1axZU+vXr7eqwdfXV6NHj1bnzp3l6uqqYsWK6auvvrIsT+0y4D/++EMvvPCC3Nzc5Orqqvr16+vUqVNp2udvv/1WFSpUkNlsVpEiRdS7d2/LsokTJ8rf31/Ozs7y9vZWz549FRsba1keEhIiDw8PLV++XOXLl5fZbNbZs2cty4cPH66CBQvKzc1NPXr0sAr1cXFx6tu3rwoVKiRHR0fVq1dPe/futSzfvHmzTCaTNmzYoBo1aihPnjyqW7euTpw4kab9AgAAAICMsLmweu3aNa1Zs0Y9e/aUk5OT1TJPT0+1a9dOP/74owzDkCSNHz9elStX1sGDBzVkyBDt2LFDPXr00DvvvKPQ0FA1adJEn376qdU4sbGxat68uTZs2KCDBw+qadOmatGihVXAk6QJEyaoRo0aOnjwoHr27Km33377viHt/PnzatCggcxmszZu3Kj9+/erc+fOunPnzkP3+YsvvlCvXr3UvXt3HTlyRMuXL1epUqUsy3PlyqWpU6fqjz/+0Ny5c7Vx40a9//77VmPcunVLn3/+uWbPnq0//vhDhQoVkiRt2LBBYWFh2rx5sxYsWKAlS5Zo+PDhlvXef/99LV68WHPnztWBAwdUqlQpBQUFpZi9/vjjjzVhwgTt27dP9vb26ty58333Jy4uTjExMVYvAAAAAEgPk5Gc+mzE7t279fTTT+uXX35J9eZAkyZN0nvvvaeLFy+qVq1aqlq1qn755RfL8tdff12xsbFasWKFpe3NN9/UihUrFBUVdd/tVqxYUT169LDMaPr6+qp+/fqaN2+eJMkwDHl6emr48OHq0aOHIiIiVLx4cR08eFBVqlTRRx99pIULF+rEiRPKnTt3uva5aNGi6tSpk0aNGpWm/osWLVKPHj105coVSf/OrHbq1EmhoaGqXLmypV9wcLB+/fVXnTt3Tnny5JEkzZo1SwMHDlR0dLT++ecf5c2bVyEhIXrjjTckSQkJCfL19VW/fv00cOBAbd68WY0aNdL69ev17LPPSpJWrlyp559/Xv/8848cHR1T1Dds2DCrQJyscp9ZsjM7pWgHAACwNfvHdcjuEoAnUkxMjNzd3RUdHS03N7cH9rW5mdVkac3QNWrUsHp/4sQJ1apVy6rt3vexsbEaMGCAypUrJw8PD7m4uCgsLCzFzGqlSpUsP5tMJnl6eurSpUup1hEaGqr69eunO6heunRJf//9tyUIpiY5KBYtWlSurq5q3769rl69qlu3bln6ODg4WNWbrHLlypagKkl16tRRbGyszp07p1OnTikhIUEBAQGW5blz51atWrUUFhZmNc7dYxcpUsRSe2oGDRqk6Ohoy+vcuXMPOQoAAAAAYM3mwmqpUqVkMplShKVkYWFhyps3rwoWLChJcnZ2Tvc2BgwYoF9++UWjR4/Wtm3bFBoaKn9//xQ3aLo3eJpMpvveuOjeS5bT6mHrRURE6IUXXlClSpW0ePFi7d+/XzNmzJBkfUMpJycnmUymDNWQFncfi+Tt3O9YmM1mubm5Wb0AAAAAID1sLqzmz59fTZo00cyZM/XPP/9YLbtw4YLmz5+v11577b7BrEyZMlY3CJKU4v2OHTsUHBysl156Sf7+/vL09FRERMQj1V2pUiVt27ZNCQkJ6VrP1dVVvr6+2rBhQ6rL9+/fr6SkJE2YMEFPP/20Spcurb///jvN4x86dMjqOO7atUsuLi7y9vZWyZIl5eDgoB07dliWJyQkaO/evSpfvny69gMAAAAAMpPNhVVJmj59uuLi4hQUFKStW7fq3LlzWr16tZo0aaKiRYumuGHS3fr06aOVK1dq4sSJCg8P15dffqlVq1ZZhVs/Pz8tWbJEoaGhOnTokN54441HftRL7969FRMTo9dff1379u1TeHi45s2bl6a75g4bNkwTJkzQ1KlTFR4ergMHDmjatGmS/p1pTkhI0LRp03T69GnNmzdPs2bNSnNd8fHx6tKli44dO6aVK1fqk08+Ue/evZUrVy45Ozvr7bff1sCBA7V69WodO3ZM3bp1061bt9SlS5cMHwsAAAAAeFQ2GVb9/Py0b98+lShRQm3atFHJkiXVvXt3NWrUSDt37lS+fPnuu25AQIBmzZqliRMnqnLlylq9erXeffddqxsBTZw4UXnz5lXdunXVokULBQUFqVq1ao9Uc/78+bVx40bFxsYqMDBQ1atX19dff52m77B27NhRkydP1syZM1WhQgW98MILCg8Pl/Tvd04nTpyozz//XBUrVtT8+fM1ZsyYNNf17LPPys/PTw0aNNBrr72mF198UcOGDbMs/+yzz9S6dWu1b99e1apV0//+9z+tWbNGefPmTfcxAAAAAIDMYnN3A84K3bp10/Hjx7Vt27bsLiVHSr7jF3cDBgAA/xXcDRjIGum5G7D9Y6rpsRo/fryaNGkiZ2dnrVq1SnPnztXMmTOzuywAAAAAQBo9kWF1z549Gjt2rG7cuKESJUpo6tSp6tq1a7bV4+Lict9lq1atUv369R9jNQAAAABg+57IsPrTTz9ldwlWQkND77usaNGij68QAAAAAPiPeCLDqq0pVapUdpcAAAAAAP8pNnk3YAAAAABAzkZYBQAAAADYHMIqAAAAAMDmEFYBAAAAADaHsAoAAAAAsDmEVQAAAACAzeHRNXhsto5qKzc3t+wuAwAAAMB/ADOrAAAAAACbQ1gFAAAAANgcwioAAAAAwOYQVgEAAAAANoewCgAAAACwOYRVAAAAAIDNIawCAAAAAGwOz1nFY9Ng8ALZmZ2yuwwAAJDD7R/XIbtLAJAGzKwCAAAAAGwOYRUAAAAAYHMIqwAAAAAAm0NYBQAAAADYHMIqAAAAAMDmEFYBAAAAADaHsAoAAAAAsDmEVQAAAACAzSGsAgAAAABsDmEVAAAAAGBzCKsAAAAAAJvznwqrw4YNU5UqVSzvg4OD1apVq0cac/PmzTKZTIqKinqkcbJKSEiIPDw8srsMAAAAAHissi2smkymB76GDRuWYp0BAwZow4YNj7/Yx8TX11eTJ0/O7jIAAAAAINvZZ9eGIyMjLT//+OOPGjp0qE6cOGFpc3FxsfxsGIYSExPl4uJi1Q4AAAAAeDJl28yqp6en5eXu7i6TyWR5f/z4cbm6umrVqlWqXr26zGaztm/fnuIy4GTjx49XkSJFlD9/fvXq1UsJCQmWZfPmzVONGjXk6uoqT09PvfHGG7p06dIDa1u8eLEqVKggs9ksX19fTZgwwWq5r6+vRo0apQ4dOsjFxUU+Pj5avny5Ll++rJYtW8rFxUWVKlXSvn37rNbbvn276tevLycnJ3l7e6tv3766efOmJKlhw4b6888/9e6771pml++2Zs0alStXTi4uLmratKlV2N+7d6+aNGmiAgUKyN3dXYGBgTpw4IDV+iaTSbNnz9ZLL72kPHnyyM/PT8uXL7fqc/ToUTVr1kwuLi4qXLiw2rdvrytXrliWL1q0SP7+/nJyclL+/PnVuHFjS/0AAAAAkJls+jurH374oT777DOFhYWpUqVKqfbZtGmTTp06pU2bNmnu3LkKCQlRSEiIZXlCQoJGjhypQ4cOaenSpYqIiFBwcPB9t7l//361adNGr7/+uo4cOaJhw4ZpyJAhVmNK0qRJkxQQEKCDBw/q+eefV/v27dWhQwe9+eabOnDggEqWLKkOHTrIMAxJ0qlTp9S0aVO1bt1ahw8f1o8//qjt27erd+/ekqQlS5boqaee0ogRIxQZGWkVRm/duqXx48dr3rx52rp1q86ePasBAwZYlt+4cUMdO3bU9u3btWvXLvn5+al58+a6ceOGVc3Dhw9XmzZtdPjwYTVv3lzt2rXTtWvXJElRUVF65plnVLVqVe3bt0+rV6/WxYsX1aZNG0n/zoS3bdtWnTt3VlhYmDZv3qyXX37Zsn93i4uLU0xMjNULAAAAANIj2y4DTosRI0aoSZMmD+yTN29eTZ8+XXZ2dipbtqyef/55bdiwQd26dZMkde7c2dK3RIkSmjp1qmrWrKnY2NhULymeOHGinn32WQ0ZMkSSVLp0aR07dkzjxo2zCrnNmzfXW2+9JUkaOnSovvjiC9WsWVOvvvqqJOmDDz5QnTp1dPHiRXl6emrMmDFq166d+vXrJ0ny8/PT1KlTFRgYqC+++EL58uWTnZ2dZQb4bgkJCZo1a5ZKliwpSerdu7dGjBhhWf7MM89Y9f/qq6/k4eGhLVu26IUXXrC0BwcHq23btpKk0aNHa+rUqdqzZ4+aNm2q6dOnq2rVqho9erSl/7fffitvb2+dPHlSsbGxunPnjl5++WX5+PhIkvz9/VM9J2PGjNHw4cNTXQYAAAAAaWHTM6s1atR4aJ8KFSrIzs7O8r5IkSJWl/nu379fLVq0ULFixeTq6qrAwEBJ0tmzZ1MdLywsTAEBAVZtAQEBCg8PV2JioqXt7pnewoULS7IOb8ltybUcOnRIISEhlu/duri4KCgoSElJSTpz5swD9zFPnjyWoJraPl68eFHdunWTn5+f3N3d5ebmptjY2BT7eHfNzs7OcnNzs6pv06ZNVvWVLVtW0r+zwpUrV9azzz4rf39/vfrqq/r66691/fr1VOsdNGiQoqOjLa9z5849cP8AAAAA4F42PbPq7Oz80D65c+e2em8ymZSUlCRJunnzpoKCghQUFKT58+erYMGCOnv2rIKCghQfH/9Itd293eTvl6bWllxLbGys3nrrLfXt2zfFWMWKFUvztpLHvvvy244dO+rq1auaMmWKfHx8ZDabVadOnRT7+KBjFRsbqxYtWujzzz9Psf0iRYrIzs5O69at0++//661a9dq2rRp+vjjj7V7924VL17cqr/ZbJbZbH7gPgEAAADAg9h0WH1Ux48f19WrV/XZZ5/J29tbklLc9Ohe5cqV044dO6zaduzYodKlS1vN4KZXtWrVdOzYMZUqVeq+fRwcHKxmb9Nqx44dmjlzppo3by5JOnfunNWNkdJa3+LFi+Xr6yt7+9Q/FiaTSQEBAQoICNDQoUPl4+OjX375Re+99166awYAAACAB7Hpy4AfVbFixeTg4KBp06bp9OnTWr58uUaOHPnAdfr3768NGzZo5MiROnnypObOnavp06db3dAoIz744AP9/***************************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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, f1_score, roc_auc_score, classification_report\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# 1. Prepare data\n", "X = df_clean.drop(\"Potability\", axis=1)\n", "y = df_clean[\"Potability\"]\n", "\n", "# 2. Train/test split\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)\n", "\n", "# 3. <PERSON>\n", "rf_model = RandomForestClassifier(n_estimators=100, max_depth=None, random_state=42)\n", "rf_model.fit(X_train, y_train)\n", "\n", "# 4. Predict and evaluate\n", "y_pred = rf_model.predict(X_test)\n", "accuracy = accuracy_score(y_test, y_pred)\n", "f1 = f1_score(y_test, y_pred)\n", "roc = roc_auc_score(y_test, rf_model.predict_proba(X_test)[:, 1])\n", "\n", "print(\"🔍 Random Forest Performance:\")\n", "print(f\"Accuracy: {accuracy:.4f}\")\n", "print(f\"F1 Score: {f1:.4f}\")\n", "print(f\"ROC AUC: {roc:.4f}\")\n", "print(\"\\nClassification Report:\")\n", "print(classification_report(y_test, y_pred))\n", "\n", "# 5. Feature importances\n", "importances = rf_model.feature_importances_\n", "feat_imp = pd.DataFrame({\"Feature\": X.columns, \"Importance\": importances})\n", "feat_imp = feat_imp.sort_values(\"Importance\", ascending=False)\n", "\n", "# Plot\n", "plt.figure(figsize=(10, 5))\n", "sns.barplot(x=\"Importance\", y=\"Feature\", data=feat_imp)\n", "plt.title(\"🔬 Feature Importances – Random Forest\")\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "98a4b4bb", "metadata": {}, "source": ["Train XGBoost Model"]}, {"cell_type": "code", "execution_count": 8, "id": "383e9481", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["⚡ XGBoost Performance:\n", "Accuracy: 0.6585\n", "F1 Score: 0.3978\n", "ROC AUC: 0.6427\n", "\n", "Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.66      0.90      0.76       400\n", "           1       0.64      0.29      0.40       256\n", "\n", "    accuracy                           0.66       656\n", "   macro avg       0.65      0.59      0.58       656\n", "weighted avg       0.65      0.66      0.62       656\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xgboost\\training.py:183: UserWarning: [22:50:35] WARNING: C:\\actions-runner\\_work\\xgboost\\xgboost\\src\\learner.cc:738: \n", "Parameters: { \"use_label_encoder\" } are not used.\n", "\n", "  bst.update(dtrain, iteration=i, fobj=obj)\n"]}, {"data": {"text/plain": ["<Figure size 1000x600 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 128202 (\\N{BAR CHART}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import xgboost as xgb\n", "from xgboost import plot_importance\n", "\n", "# 1. Create XGBoost classifier\n", "xgb_model = xgb.XGBClassifier(\n", "    n_estimators=100,\n", "    max_depth=4,\n", "    learning_rate=0.1,\n", "    random_state=42,\n", "    use_label_encoder=False,\n", "    eval_metric='logloss'\n", ")\n", "\n", "# 2. <PERSON>\n", "xgb_model.fit(X_train, y_train)\n", "\n", "# 3. Predict and evaluate\n", "y_pred_xgb = xgb_model.predict(X_test)\n", "accuracy = accuracy_score(y_test, y_pred_xgb)\n", "f1 = f1_score(y_test, y_pred_xgb)\n", "roc = roc_auc_score(y_test, xgb_model.predict_proba(X_test)[:, 1])\n", "\n", "print(\"⚡ XGBoost Performance:\")\n", "print(f\"Accuracy: {accuracy:.4f}\")\n", "print(f\"F1 Score: {f1:.4f}\")\n", "print(f\"ROC AUC: {roc:.4f}\")\n", "print(\"\\nClassification Report:\")\n", "print(classification_report(y_test, y_pred_xgb))\n", "\n", "# 4. Feature importance\n", "plt.figure(figsize=(10, 6))\n", "plot_importance(xgb_model, importance_type='gain', max_num_features=10)\n", "plt.title(\"📊 XGBoost Feature Importances\")\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "de468192", "metadata": {}, "source": ["First Uncertainty‑Aware Model — Quantile Regression Forest (QRF)"]}, {"cell_type": "code", "execution_count": 12, "id": "809a3464", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌲 Custom Quantile‑Forest Results\n", "Accuracy : 0.3415\n", "F1 Score : 0.4953\n", "ROC AUC  : 0.6753\n", "Brier    : 0.2145   (↓ lower ⇒ better calibration)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 1. Imports\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.metrics    import (accuracy_score, f1_score,\n", "                                roc_auc_score, brier_score_loss)\n", "from sklearn.calibration import calibration_curve\n", "\n", "# 2. Prepare data (reuse your train/test split)\n", "#    X_train, X_test, y_train, y_test already defined\n", "#    For “unsafe” probability we predict y=0⇒unsafe, y=1⇒safe, so invert:\n", "y_train_num = 1 - y_train.values\n", "y_test_num  = 1 - y_test.values\n", "\n", "# 3. Train a RandomForestRegressor on the binary labels\n", "rf_reg = RandomForestRegressor(\n", "    n_estimators=200,\n", "    max_depth=8,\n", "    min_samples_leaf=5,\n", "    random_state=42,\n", "    n_jobs=-1\n", ")\n", "rf_reg.fit(X_train, y_train_num)\n", "\n", "# 4. Get each tree’s prediction on X_test\n", "#    shape: (n_trees, n_samples) → transpose to (n_samples, n_trees)\n", "all_tree_preds = np.vstack([t.predict(X_test) for t in rf_reg.estimators_]).T\n", "\n", "# 5. Compute quantiles per sample\n", "lower_05    = np.quantile(all_tree_preds, 0.05, axis=1)\n", "median_pred = np.quantile(all_tree_preds, 0.50, axis=1)\n", "upper_95    = np.quantile(all_tree_preds, 0.95, axis=1)\n", "\n", "# 6. Turn median into a hard label (threshold 0.5)\n", "y_pred_qrf = (median_pred >= 0.5).astype(int)\n", "\n", "# 7. Evaluate core metrics\n", "acc_qrf   = accuracy_score(y_test, y_pred_qrf)\n", "f1_qrf    = f1_score(y_test, y_pred_qrf)\n", "roc_qrf   = roc_auc_score(y_test_num, median_pred)\n", "brier_qrf = brier_score_loss(y_test_num, median_pred)\n", "\n", "print(\"🌲 Custom Quantile‑Forest Results\")\n", "print(f\"Accuracy : {acc_qrf:.4f}\")\n", "print(f\"F1 Score : {f1_qrf:.4f}\")\n", "print(f\"ROC AUC  : {roc_qrf:.4f}\")\n", "print(f\"Brier    : {brier_qrf:.4f}   (↓ lower ⇒ better calibration)\")\n", "\n", "# 8. Reliability Diagram\n", "prob_true, prob_pred = calibration_curve(y_test_num, median_pred, n_bins=10)\n", "plt.figure(figsize=(5,5))\n", "plt.plot(prob_pred, prob_true, 'o-', label='QRF')\n", "plt.plot([0,1],[0,1],'--', color='gray')\n", "plt.title(\"Reliability Diagram (Custom QRF)\")\n", "plt.xlabel(\"Predicted P(unsafe)\")\n", "plt.ylabel(\"Observed P(unsafe)\")\n", "plt.legend()\n", "plt.grid()\n", "plt.show()\n", "\n", "# 9. Interval <PERSON> (15 random samples)\n", "idx = np.random.choice(len(X_test), 15, replace=False)\n", "plt.figure(figsize=(10,4))\n", "plt.errorbar(range(15), median_pred[idx],\n", "             yerr=[median_pred[idx]-lower_05[idx],\n", "                   upper_95[idx]-median_pred[idx]],\n", "             fmt='o', capsize=5)\n", "plt.title(\"5–95% Prediction Intervals (Custom QRF)\")\n", "plt.xlabel(\"Sample index\")\n", "plt.ylabel(\"P(unsafe)\")\n", "plt.grid()\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "fa835d57", "metadata": {}, "source": ["Takeaway:-Traditional quantile forests tend to produce collapsed intervals on noisy, low-resolution rural water data — motivating the need for smarter uncertainty estimators"]}, {"cell_type": "markdown", "id": "3bb5f436", "metadata": {}, "source": ["XGBoost & RF → higher accuracy but no confidence intervals\n", "\n", "QRF → lower accuracy, but calibrated probabilities + confidence intervals"]}, {"cell_type": "markdown", "id": "538d0d74", "metadata": {}, "source": ["Quantile-GBDT"]}, {"cell_type": "code", "execution_count": 14, "id": "af8f55e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌳 Quantile‑GBDT (5–50–95 %) Results\n", "Accuracy : 0.3902\n", "F1 Score : 0.5614\n", "ROC AUC  : 0.5000\n", "Brier    : 0.3902   (↓ lower ⇒ better)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# ────────────────────────────────────────────────────────────────────────────\n", "# Step X: Quantile‑GBDT Uncertainty Model (Lightweight NGBoost alternative)\n", "# ────────────────────────────────────────────────────────────────────────────\n", "\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn.ensemble import GradientBoostingRegressor\n", "from sklearn.metrics import (\n", "    accuracy_score, f1_score,\n", "    roc_auc_score, brier_score_loss\n", ")\n", "from sklearn.calibration import calibration_curve\n", "\n", "# 1️⃣ Prepare numeric target: P(unsafe) = 1 − y\n", "y_train_num = 1 - y_train.values\n", "y_test_num  = 1 - y_test.values\n", "\n", "# 2️⃣ Train three GBDT models for 5%, 50%, 95% quantiles\n", "gbr_lower = GradientBoostingRegressor(\n", "    loss='quantile', alpha=0.05,\n", "    n_estimators=200, max_depth=4, random_state=42\n", ").fit(X_train, y_train_num)\n", "\n", "gbr_median = GradientBoostingRegressor(\n", "    loss='quantile', alpha=0.50,\n", "    n_estimators=200, max_depth=4, random_state=42\n", ").fit(X_train, y_train_num)\n", "\n", "gbr_upper = GradientBoostingRegressor(\n", "    loss='quantile', alpha=0.95,\n", "    n_estimators=200, max_depth=4, random_state=42\n", ").fit(X_train, y_train_num)\n", "\n", "# 3️⃣ Predict on test set\n", "lower_05    = gbr_lower.predict(X_test)\n", "median_pred = gbr_median.predict(X_test)\n", "upper_95    = gbr_upper.predict(X_test)\n", "\n", "# Clip to [0,1]\n", "lower_05    = np.clip(lower_05,  0,1)\n", "median_pred = np.clip(median_pred,0,1)\n", "upper_95    = np.clip(upper_95,  0,1)\n", "\n", "# 4️⃣ Convert median to hard label\n", "y_pred_qgbdt = (median_pred >= 0.5).astype(int)\n", "\n", "# 5️⃣ Metrics\n", "acc_qgbdt   = accuracy_score(y_test, y_pred_qgbdt)\n", "f1_qgbdt    = f1_score(y_test, y_pred_qgbdt)\n", "roc_qgbdt   = roc_auc_score(y_test_num, median_pred)\n", "brier_qgbdt = brier_score_loss(y_test_num, median_pred)\n", "\n", "print(\"🌳 Quantile‑GBDT (5–50–95 %) Results\")\n", "print(f\"Accuracy : {acc_qgbdt:.4f}\")\n", "print(f\"F1 Score : {f1_qgbdt:.4f}\")\n", "print(f\"ROC AUC  : {roc_qgbdt:.4f}\")\n", "print(f\"Brier    : {brier_qgbdt:.4f}   (↓ lower ⇒ better)\")\n", "\n", "# 6️⃣ Reliability Diagram\n", "prob_true, prob_pred = calibration_curve(y_test_num, median_pred, n_bins=10)\n", "plt.figure(figsize=(5,5))\n", "plt.plot(prob_pred, prob_true, 'o-', label='Quantile‑GBDT')\n", "plt.plot([0,1],[0,1],'--', color='gray')\n", "plt.title(\"Reliability Diagram – Quantile‑GBDT\")\n", "plt.xlabel(\"Predicted P(unsafe)\")\n", "plt.ylabel(\"Observed P(unsafe)\")\n", "plt.legend()\n", "plt.grid()\n", "plt.show()\n", "\n", "# 7️⃣ Prediction Interval Plot (15 random samples)\n", "idx = np.random.choice(len(X_test), 15, replace=False)\n", "plt.figure(figsize=(10,4))\n", "plt.errorbar(\n", "    range(15), median_pred[idx],\n", "    yerr=[median_pred[idx]-lower_05[idx],\n", "          upper_95[idx]-median_pred[idx]],\n", "    fmt='o', capsize=5\n", ")\n", "plt.title(\"5–95 % Prediction Intervals (Quantile‑GBDT)\")\n", "plt.xlabel(\"Sample index\")\n", "plt.ylabel(\"P(unsafe)\")\n", "plt.grid()\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "ef78585d", "metadata": {}, "source": ["Train NGBoost – Probabilistic Classifier"]}, {"cell_type": "code", "execution_count": 20, "id": "e1f16e00", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌟 NGBoost Results\n", "Accuracy : 0.3674\n", "F1 Score : 0.5089\n", "ROC AUC  : 0.3569\n", "Brier    : 0.3407   (↓ lower ⇒ better calibration)\n"]}, {"data": {"image/png": "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********************************+QpcuXTQaDyHapqysDJs3b0ZhYSFsbGy0bttJbeZiZYylY1sjbO1lbLv8EO0aW2N4WzeuwyJELXVamSwpKQlffPEFxo4di8zMTADAsWPHcOfOHbXv9fPPP2Py5MmYNGkS/P39sXLlSpiYmGDt2rW1XiOXyzF+/HgsXLgQTZo0qctbIEQnZGZmIjExEYWFhbC3t0dERAQsLS25DkundGlqjw96+QAA5u6/hfh02vmP6Ba1a9Rnz55Fv3790KlTJ5w7dw7ffPMNHBwc8O+//2LNmjXYvXu3yvcqLy/HtWvXMGfOHOUxHo+HkJAQREdH13rdokWL4ODggP/7v//D33///dLXkEgkkEgkyseV23NKpdLX3vWr8nraPUw1VF7qSU9Px7Zt2yCTyWBvb49x48ZBLBZT+b1EbZ+x97o0xrWUHJy7l4P3Nl3D3vfaw1ws4CJErUK/k+rRZHmpcw+1E/Vnn32Gr7/+GrNnz4a5ubnyeM+ePfH777+rda/s7GzI5fJqKyk5OjoiPj6+xmvOnz+PNWvWICYmRqXXWLx4MRYuXFjt+MmTJzW2aEtkZKRG7mMoqLxeraSkBElJSZDL5TAxMYGjoyPtTqeGmj5jfS2BW0I+HuSUIGJFFN7yUYB6ESrQ76R6NFFeJSWqz0RQO1HfunULW7durXbcwcEB2dnZ6t5OLYWFhZg4cSJWrVoFOzs7la6ZM2cOZs+erXxcUFAAd3d39OnTBxYWFq8Vj1QqRWRkJHr37g2BgP46fxUqL9WVlpZiy5YtMDIygq2tLfr27UtlpoJXfcZ82uZj7OrLuJnLQ4aVL97q5NHwQWoR+p1UjybLq7J1VxVqJ2orKyukpaXB09OzyvEbN26oveiCnZ0d+Hw+MjKqLkiQkZFR49zQpKQkPHjwAIMGDVIeq9zWz8jICAkJCdU2BhGJRBCJqq/3KxAINPbB1OS9DAGV16sJBAKEhYUBAE6dOkVlpqbayqudpx3mD/THvAN38MPJe2jjYYs3PHRzL25Nos+XejRRXupcr/ZgsjFjxuDTTz9Feno6GIaBQqHAhQsX8NFHHym/WFQlFArRtm1bREVFKY8pFApERUUhODi42vm+vr64desWYmJilD+DBw9Gjx49EBMTA3d3d3XfDiFa4969e7hy5YrysampKYRCIYcR6acJHRpjSCsXyBUspm25jqxCyasvIoRDateov/32W0ybNg3u7u6Qy+Xw9/eHXC7HuHHj8MUXX6gdwOzZsxEeHo527dohKCgIS5YsQXFxMSZNmgSgYoEVV1dXLF68GGKxGAEBAVWur5xP+uJxQnRJXFwcdu/eDYVCAWtra3h7e3Mdkt5iGAaL3wxE7JMC3MsserZ5RxCM+HWaBENIvVM7UQuFQqxatQrz58/HrVu3UFRUhNatW6Np06Z1CmD06NHIysrC/PnzkZ6ejlatWuH48ePKAWYPHz4Ej0e/QER/3b59G3v37gXLsmjevHm1biWieSZCI6yY0BZDfj+P6Ps5+DnyLj7p68t1WITUSO1EXcnd3V1Zq7516xaePn0Ka2vrOt1r+vTpmD59eo3PnTlz5qXXrl+/vk6vSYg2iImJwcGDB8GyLFq0aIEhQ4bQH6YNxNvBDN+PaIHpW29g+ZkktHSzgoWxAJmFZXAwFyPI04a2yCRaQe1E/cEHHyAwMBD/93//B7lcjm7duuHixYswMTHB4cOH0b1793oIkxD9c/XqVRw5cgQA0KZNGwwcOJBWHWtgA1u44OqDp1h/8QHe23wN7HPPOVuKsWCQP/oGOHMWHyFAHQaT7d69Gy1btgQAHDp0CPfv30d8fDxmzZqFuXPnajxAQvTRkydPlEk6KCiIkjSH2jauaAlkXzienl+GKZuv4/jttIYPipDnqJ2os7OzlVOnjh49ilGjRsHHxwdvvfUWbt26pfEACdFHLi4u6Nq1Kzp16oS+fftSkuaIXMHi26NxNT5XmbgXHoqFXPFiGiek4aidqB0dHREbGwu5XI7jx4+jd+/eACpWWeHzaQs5QmrDsixkMpnycffu3dGrVy9K0hy6nJyLtPyyWp9nAaTll+FyMm3fS7ijdqKeNGkSRo0ahYCAADAMg5CQEADAP//8A19fGjVJSE1YlsWpU6ewefNm5Rq/DMNQkuZYZmHtSbou5xFSH9QeTPbll18iICAAjx49wsiRI5WrfvH5fHz22WcaD5AQXceyLI4fP47Lly8DABITE+Hn58dxVAQAHMzFGj2PkPpQp+lZI0aMqHZM0/tUE6IPWJbF4cOHcf36dQDAgAEDKElrkSBPGzhbipGeX1ZtMBkAMACcLCumahHClTpN2IyKisLAgQPh5eUFLy8vDBw4EKdOndJ0bIToNIVCgf379+P69etgGAZDhgxBu3btuA6LPIfPY7BgkD+AiqRckwWD/Gk+NeGU2ol6+fLl6Nu3L8zNzTFz5kzMnDkTFhYW6N+/P5YtW1YfMRKic+RyOfbs2YObN2+CYRi8+eabaNWqFddhkRr0DXDGiglt4GRZtXnbiMdgxYQ2NI+acK5Oa33/8ssvVVYSe//999GpUyflOuCEGLr8/HwkJyeDx+NhxIgR1Nyt5foGOKO3vxMuJ+ciObsIX+y/DZmChb+zJdehEaJ+jTovLw99+/atdrxPnz7Iz8/XSFCE6DobGxtMnDgRY8aMoSStI/g8BsFethjXvjGCvWwBAMdosROiBdRO1IMHD8a+ffuqHT9w4AAGDhyokaAI0UXl5eV48uSJ8rGzs3OdN6sh3Kps7j52O53jSAhRsel76dKlyv/39/fHN998gzNnzij3jL506RIuXLiADz/8sH6iJETLlZWVYevWrcjIyMCECRNob3QdF+rviPkHbiPmUR7S8kvhbGnMdUjEgKmUqH/55Zcqj62trREbG4vY2FjlMSsrK6xdu7ZOe1ITostKS0uxefNmPHnyBGKxmBYx0QMOFmK0bWSNqylPcfx2OiZ1oq1HCXdUStTJycn1HQchOqm4uBibNm1CRkYGjI2NMXHiRDg70yhhfdA3wAlXU57iGCVqwrE6b3ybnZ2N7OxsTcZCiE4pLCzEhg0bkJGRAVNTU0RERFCS1iN9Ayo2H7ryIBdZhRKOoyGGTK1EnZeXh2nTpsHOzg6Ojo5wdHSEnZ0dpk+fjry8vHoKkRDtU1RUhPXr1yMrKwvm5uaIiIiAg4MD12ERDXKzNkELN0uwLHAylgaVEe6oPI86NzcXwcHBSE1Nxfjx45VTTmJjY7F+/XpERUXh4sWLsLa2rrdgCdEWxsbGsLW1hVwuR3h4OH3u9VTfACfcfJyP47fTMb59Y67DIQZK5US9aNEiCIVCJCUlwdHRsdpzffr0waJFi6oNPCNEH/H5fIwaNQolJSWwsLDgOhxST/oFOOOH4wmITspBXkk5rEyEXIdEDJDKTd/79+/Hjz/+WC1JA4CTkxN++OGHGudXE6IvMjMzcfr0abBsxfYNRkZGlKT1nKedKXydzCFTsIiMzeA6HGKgVE7UaWlpaN68ea3PBwQEID2d+nGIfkpPT8eGDRvw999/49KlS1yHQxpQ5aCyE3fo+41wQ+VEbWdnhwcPHtT6fHJyMmxsaCs4on9SU1OxYcMGlJSUwMXFhTbXMDD9nq1Sdu5eNookMo6jIYZI5UQdGhqKuXPnory8vNpzEokE8+bNq3ENcEJ02cOHD7Fx40aUlZXB3d0dEydOhLExrVJlSHwczdDEzhTlMgVOx2dyHQ4xQGoNJmvXrh2aNm2KadOmwdfXFyzLIi4uDsuXL4dEIsGmTZvqM1ZCGlRycjK2bdsGqVQKDw8PjB07FkIhDSYyNAzDoG+AE5afScLx22kY3NKF65CIgVE5Ubu5uSE6OhpTp07FnDlzlANqGIZB79698fvvv9P6xkRvlJSUYPv27ZBKpfDy8sLo0aMhEAi4DotwpF+AM5afScJf8VkoLZfDWMjnOiRiQNTaj9rT0xPHjh3D06dPce/ePQCAt7c39U0TvWNiYoIBAwYgLi4Ow4cPh5GR2lu3Ez0S4GoBVytjpOaV4uzdLOUAM0IaQp2+faytrREUFKTpWAjhnFwuB59fUVtq0aIFAgMDaZMNomz+XnM+Gcdvp1GiJg2qzmt9E6JvYmJisGLFChQWFiqPUZImlfo9S85RcZmQyOQcR0MMCSVqQgBcu3YNBw4cQE5ODq5du8Z1OEQLtWlkDQdzEQolMlxMzOE6HGJAKFETg/fPP//g8OHDAIA33ngD3bp14zgioo14PAahzStq1cdup3EcDTEklKiJQTt//jyOHz8OAOjYsSP69etHzd2kVpXN35GxGZDJFRxHQwwFJWpikFiWxZkzZxAVFQUA6Nq1K0JCQihJk5cK8rSBtYkAT0uk+Cc5l+twiIGgRE0MUnl5OW7fvg0A6NmzJ3r06EFJmrySEZ+HPv7U/E0aFiVqYpBEIhHCwsIwaNAgdOnShetwiA7pG1i5SUcGFAqW42iIIaBETQwGy7J49OiR8rGFhQXatGnDYUREF3XysoO52AhZhRJcf/iU63CIAaBETQyCQqHAgQMHsG7dOmWTNyF1ITTiIcTPEQBw7DZtfUnqHyVqovfkcjn27t2Lf//9FwCU69QTUleVK5Mdv51OnydS7yhRE70mk8mwe/du3LlzBzweDyNHjkRgYCDXYREd183HHiZCPlLzSnErNZ/rcIieo0RN9JZUKsWOHTsQHx8PPp+P0aNHw8/Pj+uwiB4QC/jo0cwBADV/k/pHiZroJZlMhm3btiExMRFGRkYYN24cfHx8uA6L6BFq/iYNhRI10Ut8Ph8ODg4QCoWYMGECmjRpwnVIRM/08HWA0IiH5OxiJGQUvvoCQuqIEjXRSwzDIDQ0FO+++y4aN27MdThED5mJjNC1qR0A4Ngtav4m9YcSNdEbJSUlOHHiBGQyGYCKZG1jY8NxVESf9Q1wBlDR/E1IfTHiOgBCNKGoqAgbN25EVlYWJBIJBg8ezHVIxAD09nOEEY9BQkYh7mcVoYm9GdchET1ENWqi8woKCrB+/XpkZWXB3NwcHTt25DokYiAsTQQI9rIFQKO/Sf2hRE10Wl5eHtatW4ecnBxYWloiIiICdnZ2XIdFDEg/av4m9YwSNdFZOTk5WLduHfLy8mBtbY1JkyZRnzRpcH2aO4LHALdS8/Eot4TrcIgeokRNdJJCocC2bdtQUFAAOzs7TJo0CZaWllyHRQyQnZkIb3hU/IF44g7VqonmUaImOonH42Hw4MFwc3NDREQEzM3NuQ6JGLB+zy1+QoimUaImOkUulyv/v1GjRnjrrbdgamrKYUSE/DdN69rDp8gsKOM4GqJvKFETnfHw4UP89ttvSE//r9bCMAyHERFSwclSjNaNrMCy1PxNNI8SNdEJycnJ2Lx5M/Lz83Hu3DmuwyGkmsrmb5qmRTSNEjXReomJidi6dSukUimaNGmCYcOGcR0SIdVUTtP6JzkXucXlHEdD9AklaqLV4uPjsX37dshkMvj4+GDs2LEQCARch0VINe42JmjuYgG5gkVkLNWqieZQoiZa686dO9i1axfkcjn8/PwwatQoGBnRqrdEe1HzN6kPlKiJVmJZFtevX4dCoUBgYCBGjBgBPp/PdViEvFTlHtUXErORXyrlOBqiLyhRE63EMAxGjx6NkJAQDB06FDwefVSJ9vN2MIe3gxmkchan4zO4DofoCfr2I1rl0aNHYFkWACAUCtGpUydK0kSnKJu/aY9qoiH0DUi0xoULF7B27VqafkV0WmXz99m7WSiWyDiOhugDStSEcyzL4uzZszh16hSAitXHKmvVhOgaf2cLNLIxgUSmwJmELK7DIXqAEjXhFMuyOH36NM6cOQMA6NGjB3r27EkrjhGdxTDMf2t/0yplRAMoURPOsCyLEydO4Pz58wCAPn36oGvXrhxHRcjrq2z+Ph2XgTKp/BVnE/JyWpGoly1bBg8PD4jFYrRv3x6XL1+u9dxVq1ahS5cusLa2hrW1NUJCQl56PtFex44dwz///AMA6N+/P4KDgzmOiBDNaOlmBWdLMYrL5Th/L5vrcIiO4zxR79ixA7Nnz8aCBQtw/fp1tGzZEqGhocjMzKzx/DNnzmDs2LH466+/EB0dDXd3d/Tp0wepqakNHDl5XQ4ODmAYBoMHD8Ybb7zBdTiEaAyPxyC0OS1+QjSD80T9888/Y/LkyZg0aRL8/f2xcuVKmJiYYO3atTWev2XLFkydOhWtWrWCr68vVq9eDYVCgaioqAaOnLyudu3aYerUqWjdujXXoRCicZX91KfiMiCVKziOhugyThN1eXk5rl27hpCQEOUxHo+HkJAQREdHq3SPkpISSKVS2NjY1FeYREPkcjmePHmCkpIS5TE7OzsOIyKk/rTzsIGdmRD5pVJEJ+VwHQ7RYZwunJydnQ25XA5HR8cqxx0dHREfH6/SPT799FO4uLhUSfbPk0gkkEgkyscFBQUAAKlUCqn09Zb4q7z+de9jCGQyGXbv3o3MzEzs3LkT4eHhNLJbBfQZU4+2lVeInwO2X3mMIzefINjTiutwqtG28tJ2miwvde6h0zscfPfdd9i+fTvOnDkDsVhc4zmLFy/GwoULqx0/efIkTExMNBJHZGSkRu6jr+RyOZKTk1FUVASGYWBsbIxjx45xHZZOoc+YerSlvKyLGAB8HPn3EToYPQBPS/821Zby0hWaKK/nWxZfhdNEbWdnBz6fj4yMqmviZmRkwMnJ6aXX/vjjj/juu+9w6tQptGjRotbz5syZg9mzZysfFxQUKAegWVhYvFb8UqkUkZGR6N27N229WAuJRIKdO3eiqKgIAoEAjRs3xrBhw6i8VESfMfVoW3n1liuw9fszyC+Vwd6/A9p7alcXnbaVl7bTZHlVtu6qgtNELRQK0bZtW0RFRWHo0KEAoBwYNn369Fqv++GHH/DNN9/gxIkTaNeu3UtfQyQSQSQSVTsuEAg09sHU5L30SVlZGXbs2IHHjx9DJBJh9OjRuHnzJpVXHVCZqUdbyksgAEL8nLDn+mOcis9GZx/HV1/EAW0pL12hifJS53rOR33Pnj0bq1atwoYNGxAXF4cpU6aguLgYkyZNAgCEhYVhzpw5yvO///57zJs3D2vXroWHhwfS09ORnp6OoqIirt4CqcXBgwfx+PFjGBsbIywsDG5ublyHREiDU65SdjsdCgUtjUvUx3kf9ejRo5GVlYX58+cjPT0drVq1wvHjx5UDzB4+fFhl96QVK1agvLwcI0aMqHKfBQsW4Msvv2zI0Mkr9O7dG3l5eRgyZAgcHR1pwAoxSJ2b2sFUyEd6QRliHuehTSNrrkMiOobzRA0A06dPr7Wpu3IN6EoPHjyo/4BIncnlcvD5fACAtbU1Jk+eTKO7iUETC/jo6eeIQ/8+wfHb6ZSoido4b/om+iMvLw8rVqxAQkKC8hglaUKe26P6dhrtDEfURomaaERubi7WrVuHnJwcREVFQaGglZgIqdS9mT3EAh4e5ZYiNk310b6EAJSoiQZkZWVh3bp1KCgogK2tLSZMmFBlXAEhhs5EaIRuPvYAKgaVEaIO+jYlryUjIwPr169HUVERHBwcEBER8drz0wnRR/0CnAHQJh1EfZSoSZ09efIEGzZsQElJCZycnBAeHg4zMzOuwyJEK/X0c4CAzyAxswiJmYVch0N0CCVqUmc3b95EaWkpXF1dER4errElWQnRRxZiATp7V2xCc+wW1aqJ6ihRkzrr06cPevXqhYkTJ9a61joh5D/U/E3qghI1UcuTJ0+UI7p5PB46d+5c4xKthJDqevs7gs9jEJtWgIc5qm/KQAwbJWqisoSEBKxduxb79++n6VeE1IG1qRAdmlRszHHsdhrH0RBdQYmaqCQ2NhY7d+6EXC6HVCqlRRsIqaO+1PxN1ESJmrzSzZs3sXv3bigUCgQEBGDEiBHKZUIJIeoJ9XcEwwAxj/KQll/KdThEB1CiJi9148YN7Nu3DyzLolWrVhg2bBglaUJeg4OFGG2frfdNi58QVVCiJrW6evUqDh48CABo27YtBg8eTCuOEaIBfZVrf1OiJq9G37qkVtbW1uDz+Wjfvj0GDBhAG2wQoiGVifrKg1xkFUo4joZoO0rUpFZeXl549913ERoaSkmaEA1yszZBCzdLsCwQGZvBdThEy1GiJkosy+L8+fPIzs5WHrO3t6ckTUg96Pvc1peEvAwlagKgIklHRkYiKioKGzduhERCzXGE1KfKVcqik3KQXyLlOBrNkytYRCfl4EBMKqKTciBX0JTOujLiOgDCPZZlcezYMVy5cgUAaLUxQhqAp50pfJ3MEZ9eiMi4DIxo68Z1SBpz/HYaFh6KRVp+mfKYs6UYCwb5K+eRE9VRjdrAKRQKHDp0SJmkBw0ahKCgII6jIsQwVDZ/H9ej5u/jt9MwZfP1KkkaANLzyzBl83W9eq8NhRK1AVMoFNi/fz9u3LgBhmEwbNgwtGnThuuwCDEYlc3f5+5lo0gi4zia1ydXsFh4KBY1NXJXHlt4KJaawdVEidqAnT17Frdu3QKPx8OIESPQokULrkMixKD4OJqhiZ0pymUKnI7P5Dqc13Y5ObdaTfp5LIC0/DKcTcikZYjVQH3UBiw4OBj3799H586d0axZM67DIcTgMAyDvgFOWH4mCcdvp2FwSxeuQ6qT3OJyXEzKxpZLKSqd/9aGqzAR8uFoIYajhejZf8VwMK/4fydLMRzNxXCwEEEs4G4lRLmCxeXkXGQWlsHBXIzWbuacxEGJ2sAoFArl6mJisRhvvfUWTb8ihEP9Apyx/EwS/orPQmm5HMZC7V+it0wqx5UHuTh/LxvnE7Nx50mB2vcoKZcjObsYydnFLz3P0lhQJZlX/f+Kx3ZmIgj4mm0grmlAnJOFCP2dGPTX6Cu9GiVqAyKRSLBt2zb4+vqiQ4cOAEBJmhCOBbhawM3aGI+fluLs3SzlADNtIlewuPMkH+cTs3H+XjaupjxFuazqVre+Tubo6GWLfTdSkVcirbGfmgHgZCnGyVldkVNUjoyCMqQXlCGzQIKMgjJkFD7777OfMqkC+aVS5JdKcTejqNb4GAawNRXByVL0rCb+X0J3sqiomTtaiGFjIgSP9+rvvMoBcS++h4wCCdYW8NDmTgYGtmq4UfqUqA1EWVkZtmzZgsePHyM9PR2BgYEwNTXlOixCDB7DMOjb3Amrzyfj+O00rUnUKTnFOJ+YjQuJ2biYlIO8F+Z6O1uK0dnbDp2b2qGjlx3szSumdAZ52mDK5utggCqJrjI9LhjkD3OxAOZiATzsav8OYlkWBWUyZD5L5hnPkvnzjzMLypBZKIFMwSK7SILsIgluo/bavRGPqWhef9a07mghepbUKxK6o4UItmYifPmKAXHfHItHvxau4KuQ9DWBErUBKCkpwebNm5GWlgaxWIyJEydSkiZEi/QNqEjUUXGZkMjkEBk1fPP30+JyXEiqSMznE7PxKLfqFpzmIiMEe9mic1M7dPK2QxM70xpb5PoGOGPFhDbVm43VnEfNMAwsjQWwNBagqWPtfcMKBYvcknKk55chs/C/hJ5R8Pz/S5BTXJHQn+SX4clLBrypEBnS8iW4nJyLYC/b17iP6ihR67ni4mJs3LgRmZmZMDExwcSJE+HkpB1/sRNCKrRpZA0HcxEyCyW4mJiDHr4O9f6aZVI5LiXl4GAKD3+uiEZsWiGeH4gt4DNo3cgaXbzt0KmpHVq4WsJIxX7gvgHO6O3vVGUgVpCnTb3UQHk8BnZmFf3UgGWt50nlCmQV/pe4MwvLkJ7/3/9nFFQ8LihTbZpcZuHrJHv1UKLWY4WFhdi4cSOys7NhZmaGsLAw2Nvbcx0WIeQFPB6D0OZO2HQpBcdup9VLopYrWMQ+KajoZ07MwpUHlf3MPACFACr6mTs9a84O8rCBqajuKYLPYxqsxqkKAZ8HFytjuFgZv/S8swmZCF935ZX3czAXayq0V6JErcfu3r2L7OxsWFhYICwsDLa22vNLQwipql9ARaKOjM2ATK5Qufb6Mg9zSpT9zBeSsqv1MztaiNBYVIpR3VqiazOHBk0+2qpzU3s4W4qRnl9WYz81wMLZsqKFoKFQotZjbdu2hVwuh4+PD6ysrLgOhxDyEkGeNrA2EeBpiRSXk3PR0dtO7Xs8LS7HxaQcZXJ+mFtS5XlzkRE6eNmis3dFP3MjKyGOHTuG/i2dIRAINPVWdBqfx2DBIP9aB8SxAOb2822wgWQAJWq9k5OTA1NTU4jFFX8Z07rdhOgGIz4PffydsOPqI6y78ABZRZJX9u2WSeW4lvIUf9+rSMy3n+RX6Wc24jFo08haOQCspVvVfmapVP927dKE2gfEidDPsQShzR0bNB5K1HokIyMDmzZtgo2NDSZMmAChUMh1SIQQNdiZV/zORsZlIDIuA0DVXacUChaxaQXK+cxXHuRC8sJ85maOFf3MXZraIcjz9fqZDVlNA+Jau5njxPFjDR4L/QvqibS0NGzatAmlpaUwNzeHTCajRE2IDjl+Ow3L/0qqdjwtvwzvbb6ONo2tkJxVjKc19DN39rZH56a26ORlBwcL6mfWlBcHxHHVAkGJWg88fvwYmzdvhkQigaurK8aPHw9j45ePbCSEaI+X7TpV6XpKHgDATGSEDk1s0dm7Yk6zl70ZrTCo5yhR67iUlBRs3boV5eXlaNSoEcaNGweRSMR1WIQQNbxq16lKCwf7Y1z7xhpf15poN0rUOiw5ORlbt26FTCaDp6cnxowZQ83dhOggVRfPsDIRUpI2QJSodZiZmRmEQiE8PT0xcuRIml5BiI5Sdf4yzXM2TJSodZi9vT3eeustWFpawsiI/ikJ0VVBnjYvXWSjctephlxkg2gPakPRMbdv30ZycrLysa2tLSVpQnRc5SIbwH+7TFV6ftephlxkg2gPStQ6JCYmBnv27MG2bduQlZXFdTiEEA2qXGTDybJq87aTpRgrJrRRedcpon+oKqYjrl69iiNHjgAAWrRoATs79ZcXJIRot4bcdYroDkrUOuDSpUs4ceIEAKB9+/YIDQ2leZOE6Clt23WKcI8StZb7+++/cfr0aQBAp06d0KtXL0rShBBiQChRa7HY2Fhlku7evTu6du1KSZoQQgwMJWot1qxZM/j6+sLNzQ2dOnXiOhxCCCEcoEStZdhne9QxDAM+n49Ro0ZRLZoQQgwYTc/SIgqFAocOHcLhw4erJGxCCCGGi2rUWkKhUODAgQO4efMmGIZB69at4ebmxnVYhBBCOEaJWgvI5XLs3bsXsbGxYBgGb775JiVpQgghAChRc04mk2H37t1ISEgAj8fDyJEj4evry3VYhBBCtAQlag5JpVLs2LEDSUlJMDIywqhRo9C0aVOuwyKEEKJFKFFzKDU1FcnJyRAIBBg7diw8PT25DokQQoiWoUTNIQ8PD7z55pswNzdHo0aNuA6HEEKIFqJE3cBKS0shkUhgZWUFAGjevDm3ARFCdIZcLodUKtXY/aRSKYyMjFBWVga5XK6x++ordcqLz+fDyMhII1NsKVE3oOLiYmzatAkSiQSTJk2ChYUF1yERQnREUVERHj9+rFxjQRNYloWTkxMePXpEazaoQN3yMjExgbOzM4RC4Wu9LiXqBlJYWIiNGzciOzsbpqamkEgkXIdECNERcrkcjx8/homJCezt7TWWVBUKBYqKimBmZgYej9a/ehVVy4tlWZSXlyMrKwvJyclo2rTpa5UvJeoGkJ+fj40bNyI3NxcWFhYICwuDrS1tY0cIUY1UKgXLsrC3t4exsbHG7qtQKFBeXg6xWEyJWgXqlJexsTEEAgFSUlKU19QVJep69vTpU2zcuBF5eXmwsrJCWFgYrK2tuQ6LEKKDqHlat2jqjx9K1PUoJycHGzZsQGFhIWxsbBAWFgZLS0uuwyKEEKJDKFHXI7FYrPyZOHEizM3NuQ6JEEKIjqFOiXpkamqKiRMnIjw8nJI0IYRzcgWL6KQcHIhJRXRSDuQKzY0gr0lERAQYhsF3331X5fj+/furNOOzLItVq1YhODgYFhYWMDMzQ/PmzTFz5kwkJiYqz/vyyy/BMIzyx9LSEl26dMHZs2fr9X28qEmTJliyZEmDvR4lag1LTU1FTEyM8rG5uTlMTU25C4gQQgAcv52Gzt+fxthVlzBzewzGrrqELj+cQVRCTr2+rlgsxvfff4+nT5/W+DzLshg3bhzef/999O/fHydPnkRsbCzWrFkDsViMr7/+usr5zZs3R1paGtLS0hAdHY2mTZti4MCByM/Pr9f3wSVK1Br08OFDbNy4EQcOHMDdu3e5DocQQgBUJOkpm68jLb+syvGMgjJ8tC8ex2+n19trh4SEwMnJCYsXL67x+R07dmD79u3YsWMH5s2bhw4dOqBRo0bo0KEDvv/+e6xbt67K+UZGRnBycoKTkxP8/f2xaNEiFBUVVfnOffjwIYYMGQIzMzNYWFhg1KhRyMjIqHKfFStWwMvLC0KhEM2aNcOmTZuUz7Esiy+//BKNGjWCSCSCi4sL3n//fQDAwIEDkZKSglmzZilr9vVNKxL1smXL4OHhAbFYjPbt2+Py5csvPX/Xrl3w9fWFWCxGYGAgjh492kCR1u7+/fvYvHkzysvL4eHhAQ8PD65DIoToKZZlUVIuU+mnsEyKBQfvoKZGbvbZz6LDsSgsk6p0P3UXXOHz+fj222/x22+/4fHjx9We37ZtG5o1a4bBgwfXeP3LEqFEIsG6detgZWWFZs2aAaiYQjVkyBDk5ubi7NmziIyMxP379zF69Gjldfv27cPMmTPx4Ycf4vbt23j33XcxadIk/PXXXwCAPXv24JdffsEff/yBe/fuYf/+/QgMDAQAbNq0CW5ubli0aJGyZl/fOB9MtmPHDsyePRsrV65E+/btsWTJEoSGhiIhIQEODg7Vzr948SLGjh2LxYsXY+DAgdi6dSuGDh2K69evIyAggIN3ACQmJmLPnj2Qy+Xw9vbGqFGjIBAIOImFEKL/SqVy+M8/obH7pRdIEPjlSZXOjV0UChOheqlj2LBhaNWqFRYsWIA1a9ZUee7u3bvKJFvpgw8+wOrVqwEAVlZWVRL8rVu3YGZmBgAoKSmBubk5duzYoVzpMSoqCrdu3UJycjLc3d0BABs3bkTz5s1x5coVvPHGG/jxxx8RERGBqVOnAgBmz56NS5cu4ccff0SPHj3w8OFDODk5ISQkBAKBAI0aNUJQUBAUCgWsra3B5/Nhbm4OJycntcqhrjivUf/888+YPHkyJk2aBH9/f6xcuRImJiZYu3Ztjef/+uuv6Nu3Lz7++GP4+fnhq6++Qps2bfD77783cOQV8vLysHv3bsjlcjRr1gyjR4+mJE0IIS/4/vvvsWHDBsTFxb3y3Llz5yImJgbz589HUVFRleeaNWuGmJgYxMTE4Nq1a5gyZQpGjhyJq1evAgDi4uLg7u6uTNIA4O/vDysrK+Vrx8XFoVOnTlXu26lTJ+XzI0eORGlpKZo0aYLJkydj3759kMlkr/X+XwenNery8nJcu3YNc+bMUR7j8XgICQlBdHR0jddER0dj9uzZVY6FhoZi//79NZ4vkUiqLNdZUFAAoGKln9dd3P7Jkyd48OABAMDPzw+DBw8Gy7IaXTRfn1SWC5WP6qjM1KOv5VW5MplCoYBCoYCIz+D2l71VuvZyci7e2nDtleetDW+LIE+bV54n4jNQKBQqvTbLssq4O3fujD59+uCzzz5DeHg4gIpmam9vbyQkJFS5p62tLWxtbWFnZ6c8r/J+QqEQTZo0UZ7bsmVL7N+/H7/88gs2bdqkbJqvKcbK8nvx/yvvXXnc1dUVcXFxOHXqFE6dOoWpU6fif//7H06fPl3l/FeVg0KhUOYEPp9f5Tl1PqOcJurs7GzI5XI4OjpWOe7o6Ij4+Pgar0lPT6/x/PT0mgdDLF68GAsXLqx2/OTJkzAxMalj5BVYloWDgwOkUimEQiFOnNBcU5Q+i4yM5DoEnUNlph59K6/KAVRFRUUoLy9X69qWjiI4mguRWVheYz81A8DBXIiWjiLIykpeeb/CsleeoiSVSiGTyZQVpLlz56Jr167KMTwFBQUYMmQIJk+ejO3bt6N///5Vri8rKwPLssrrJRIJ5HK58rHyPTAMCgsLUVBQgEaNGuHRo0eIjY2Fm5sbACA+Ph55eXlo3LgxCgoK0LRpU5w9exbDhg1T3uPs2bNo2rRplXt369YN3bp1Q1hYGIKCgvDPP/+gZcuWMDIyQnFxcbU4XlReXo7S0lKcO3euWo28pOTVZV2J8z7q+jZnzpwqNfCCggK4u7ujT58+r717lVQqxcmTJ9G7d+/X3h3FEEilUkRGRqJ3797UPaAiKjP16Gt5lZWV4dGjRzAzM6vTmtELBjXHtK03wABVkjWjfN4f1laaXzVRIBDAyMhI+V0bHByMcePG4c8//wQAWFhYYNKkSTh+/Dj+7//+D5999hn69OkDR0dHpKSk4ODBg1WuF4lEFQPpniW5wsJC7Ny5E/Hx8fj0009hYWGBwYMHIzAwEFOnTsXPP/8MmUyG6dOnK5MuAHz66acYM2YM3njjDYSEhODw4cM4dOgQTp48CQsLC6xfvx5yuRzt27eHiYkJDhw4AGNjY/j5+QEAPD09cfnyZRQWFkIkEilr/i8qKyuDsbExunbtWu3f7VVJ/nmcJmo7Ozvw+fxqw+YzMjJq7aR3cnJS63yRSASRSFTtuEAg0MgvMsMwEAqFevWlUN80VfaGhMpMPfpWXnK5HAzDgMfj1Wn96P4tXLCCx2DhodgqU7ScLMX4qKcH+gY418umHJXTl56/91dffYWdO3cC+G8t7J07d2LVqlVYt24d/ve//0EqlcLNzQ29evXCL7/8ojyPYRjcuXMHrq6uACq2kfTy8sKKFSsQERGhfI0DBw5gxowZ6N69O3g8Hvr27YvffvtNeZ8333wTv/76K3788UfMmjULnp6eWLduHXr27AkAsLGxwXfffYePPvoIcrkcgYGBOHToEOzs7FBQUICFCxdiypQpaNq0KSQSSa0j4Xk8HhiGqfHzqM7nk2E1ublpHbRv3x5BQUH47bffAFS06Tdq1AjTp0/HZ599Vu380aNHo6SkBIcOHVIe69ixI1q0aIGVK1e+8vUKCgpgaWmJ/Px8jdSojx49iv79++vVl0J9ofJSH5WZevS1vMrKypCcnAxPT8/X2oVJrmBxOTkXmYVlcDAXo11jKxQXFcLCwoJ2z1KBQqFAQUGByuX1sn83dXIR503fs2fPRnh4ONq1a4egoCAsWbIExcXFmDRpEgAgLCwMrq6uysnyM2fORLdu3fDTTz9hwIAB2L59O65evapsSiGEEFIzPo9BsNd/W+yqOiiMcIvzRD169GhkZWVh/vz5SE9PR6tWrXD8+HHlgLGHDx9W+culY8eO2Lp1K7744gt8/vnnaNq0Kfbv38/ZHGpCCCGkPnGeqAFg+vTpmD59eo3PnTlzptqxkSNHYuTIkfUcFSGEEMI96pQghBBCtBglakIIIUSLUaImhBAdwfEkHaImTf17UaImhBAtV7n8pLqrkhFuVS7M8rpTBbViMBkhhJDaGRkZwcTEBFlZWRAIBBqb86xQKFBeXo6ysjKaR60CVcurcvW0zMxMWFlZVVvnW12UqAkhRMsxDANnZ2ckJycjJSVFY/dlWRalpaUwNjZ+6b7PpIK65WVlZaWRrTApURNCiA4QCoVo2rSpRpu/pVIpzp07h65du+rVSm71RZ3yEggEr12TrkSJmhBCdASPx3utJURfxOfzIZPJIBaLKVGrgKvyok4JQgghRItRoiaEEEK0GCVqQgghRIsZXB915QR0dTbtro1UKkVJSQkKCgqof0cFVF7qozJTD5WXeqi81KPJ8qrMQaosimJwibqwsBAA4O7uznEkhBBCDF1hYSEsLS1feg7DGtiadAqFAk+ePIG5uflrzxssKCiAu7s7Hj169MqNvwmVV11QmamHyks9VF7q0WR5sSyLwsJCuLi4vHKxGYOrUfN4PLi5uWn0nhYWFvQhVwOVl/qozNRD5aUeKi/1aKq8XlWTrkSDyQghhBAtRomaEEII0WKUqF+DSCTCggULIBKJuA5FJ1B5qY/KTD1UXuqh8lIPV+VlcIPJCCGEEF1CNWpCCCFEi1GiJoQQQrQYJWpCCCFEi1GifoVly5bBw8MDYrEY7du3x+XLl196/q5du+Dr6wuxWIzAwEAcPXq0gSLVDuqU16pVq9ClSxdYW1vD2toaISEhryxffaTuZ6zS9u3bwTAMhg4dWr8Bahl1yysvLw/Tpk2Ds7MzRCIRfHx8DOr3Ut3yWrJkCZo1awZjY2O4u7tj1qxZKCsra6BouXXu3DkMGjQILi4uYBgG+/fvf+U1Z86cQZs2bSASieDt7Y3169drPjCW1Gr79u2sUChk165dy965c4edPHkya2VlxWZkZNR4/oULF1g+n8/+8MMPbGxsLPvFF1+wAoGAvXXrVgNHzg11y2vcuHHssmXL2Bs3brBxcXFsREQEa2lpyT5+/LiBI+eOumVWKTk5mXV1dWW7dOnCDhkypGGC1QLqlpdEImHbtWvH9u/fnz1//jybnJzMnjlzho2JiWngyLmhbnlt2bKFFYlE7JYtW9jk5GT2xIkTrLOzMztr1qwGjpwbR48eZefOncvu3buXBcDu27fvpeffv3+fNTExYWfPns3Gxsayv/32G8vn89njx49rNC5K1C8RFBTETps2TflYLpezLi4u7OLFi2s8f9SoUeyAAQOqHGvfvj377rvv1muc2kLd8nqRTCZjzc3N2Q0bNtRXiFqnLmUmk8nYjh07sqtXr2bDw8MNKlGrW14rVqxgmzRpwpaXlzdUiFpF3fKaNm0a27NnzyrHZs+ezXbq1Kle49RGqiTqTz75hG3evHmVY6NHj2ZDQ0M1Ggs1fdeivLwc165dQ0hIiPIYj8dDSEgIoqOja7wmOjq6yvkAEBoaWuv5+qQu5fWikpISSKVS2NjY1FeYWqWuZbZo0SI4ODjg//7v/xoiTK1Rl/I6ePAggoODMW3aNDg6OiIgIADffvst5HJ5Q4XNmbqUV8eOHXHt2jVl8/j9+/dx9OhR9O/fv0Fi1jUN9Z1vcGt9qyo7OxtyuRyOjo5Vjjs6OiI+Pr7Ga9LT02s8Pz09vd7i1BZ1Ka8Xffrpp3Bxcan2wddXdSmz8+fPY82aNYiJiWmACLVLXcrr/v37OH36NMaPH4+jR48iMTERU6dOhVQqxYIFCxoibM7UpbzGjRuH7OxsdO7cGSzLQiaT4b333sPnn3/eECHrnNq+8wsKClBaWgpjY2ONvA7VqIlW+O6777B9+3bs27cPYrGY63C0UmFhISZOnIhVq1bBzs6O63B0gkKhgIODA/7880+0bdsWo0ePxty5c7Fy5UquQ9NKZ86cwbfffovly5fj+vXr2Lt3L44cOYKvvvqK69AMGtWoa2FnZwc+n4+MjIwqxzMyMuDk5FTjNU5OTmqdr0/qUl6VfvzxR3z33Xc4deoUWrRoUZ9hahV1yywpKQkPHjzAoEGDlMcUCgUAwMjICAkJCfDy8qrfoDlUl8+Ys7MzBAIB+Hy+8pifnx/S09NRXl4OoVBYrzFzqS7lNW/ePEycOBFvv/02ACAwMBDFxcV45513MHfu3Fdux2hoavvOt7Cw0FhtGqAada2EQiHatm2LqKgo5TGFQoGoqCgEBwfXeE1wcHCV8wEgMjKy1vP1SV3KCwB++OEHfPXVVzh+/DjatWvXEKFqDXXLzNfXF7du3UJMTIzyZ/DgwejRowdiYmLg7u7ekOE3uLp8xjp16oTExETlHzQAcPfuXTg7O+t1kgbqVl4lJSXVknHlHzksrTZdTYN952t0aJqe2b59OysSidj169ezsbGx7DvvvMNaWVmx6enpLMuy7MSJE9nPPvtMef6FCxdYIyMj9scff2Tj4uLYBQsWGNz0LHXK67vvvmOFQiG7e/duNi0tTflTWFjI1VtocOqW2YsMbdS3uuX18OFD1tzcnJ0+fTqbkJDAHj58mHVwcGC//vprrt5Cg1K3vBYsWMCam5uz27ZtY+/fv8+ePHmS9fLyYkeNGsXVW2hQhYWF7I0bN9gbN26wANiff/6ZvXHjBpuSksKyLMt+9tln7MSJE5XnV07P+vjjj9m4uDh22bJlND2LC7/99hvbqFEjVigUskFBQeylS5eUz3Xr1o0NDw+vcv7OnTtZHx8fVigUss2bN2ePHDnSwBFzS53yaty4MQug2s+CBQsaPnAOqfsZe56hJWqWVb+8Ll68yLZv354ViURskyZN2G+++YaVyWQNHDV31CkvqVTKfvnll6yXlxcrFotZd3d3durUqezTp08bPnAO/PXXXzV+J1WWUXh4ONutW7dq17Rq1YoVCoVskyZN2HXr1mk8Lto9ixBCCNFi1EdNCCGEaDFK1IQQQogWo0RNCCGEaDFK1IQQQogWo0RNCCGEaDFK1IQQQogWo0RNCCGEaDFK1IQQQogWo0RNCCGEaDFK1IRoqYiICAwdOlT5uHv37vjggw8aPI4zZ86AYRjk5eW91n3Ky8vh7e2NixcvaiYwDSkpKcHw4cNhYWGh8vs8fvw4WrVqVWWzD0LqCyVqQtQQEREBhmHAMAyEQiG8vb2xaNEiyGSyen/tvXv3qrwvsKaSq6o8PDyU5WJqaoo2bdpg165dVc5ZuXIlPD090bFjxwaJSVUbNmzA33//jYsXLyItLQ2WlpavvKZv374QCATYsmVLA0RIDB0lakLU1LdvX6SlpeHevXv48MMP8eWXX+J///tfjeeWl5dr7HVtbGxgbm6usftp2qJFi5CWloYbN27gjTfewOjRo5W1Z5Zl8fvvv+P//u//OI6yuqSkJPj5+SEgIABOTk5gGEal6yIiIrB06dJ6jo4QStSEqE0kEsHJyQmNGzfGlClTEBISgoMHDwL4r7n6m2++gYuLC5o1awYAePToEUaNGgUrKyvY2NhgyJAhePDggfKecrkcs2fPhpWVFWxtbfHJJ59U2//3xaZviUSCTz/9FO7u7hCJRPD29saaNWvw4MED9OjRAwBgbW0NhmEQEREBoGI/4sWLF8PT0xPGxsZo2bIldu/eXeV1jh49Ch8fHxgbG6NHjx5V4nwZc3NzODk5wcfHB8uWLYOxsTEOHToEALh27RqSkpIwYMAA5fk11fpjYmLAMIzyNdevXw8rKyucOHECfn5+MDMzU/6h9Px9goKCYGpqCisrK3Tq1AkpKSkAKpLwkCFD4OjoCDMzM7zxxhs4depUlTL96aefcO7cOTAMg+7duyvL9qOPPoKrqytMTU3Rvn17nDlzpsr7HTRoEK5evYqkpCSVyoeQuqJETchrMjY2rlJzjoqKQkJCAiIjI3H48GFIpVKEhobC3Nwcf//9Ny5cuKBMOJXX/fTTT1i/fj3Wrl2L8+fPIzc3F/v27Xvp64aFhWHbtm1YunQp4uLi8Mcff8DMzAzu7u7Ys2cPACAhIQFpaWn49ddfAQCLFy/Gxo0bsXLlSty5cwezZs3ChAkTcPbsWQAVf1C8+eabGDRoEGJiYvD222/js88+U7tMjIyMIBAIlO/v77//ho+PT51aBEpKSvDjjz9i06ZNOHfuHB4+fIiPPvoIACCTyTB06FB069YNN2/eRHR0NN555x1lrbioqAj9+/dHVFQUbty4gb59+2LQoEF4+PAhgIruhMmTJyM4OBhpaWnYu3cvAGD69OmIjo7G9u3bcfPmTYwcORJ9+/bFvXv3lHE1atQIjo6O+Pvvv9V+T4SoReMbZxKix57f/1mhULCRkZGsSCRiP/roI+Xzjo6OrEQiUV6zadMmtlmzZqxCoVAek0gkrLGxMXvixAmWZVnW2dmZ/eGHH5TPS6VS1s3Nrcpe0926dWNnzpzJsizLJiT8f3v3FhLVFgZw/O8ZLzmOgWjJJGmRaWNYjAY5QYWl2EuUJUVKKkkXLJRQsQGDiUINujxI2Jv6ohgkPjgQ+WJFZRdLk8hwZDAKwS4QTDplus6DuHEf9aR2Os3hfL+3ddnLb2+Rb6+LM68VoNrb22eNc+p7dad/j7DX61VGo1E9ePBA17egoEAdOnRIKaWU3W5XCQkJuvby8vIZY/1VTEyMunr1qnZvlZWVClBtbW1KKaWKi4vVjh07fhjj8+fPFaDcbrdSSqm6ujoFKJfLpfW5du2aioyMVEop9fHjRwWojo6OOWP7q/Xr16uamhqtXFxcrPuO4cHBQWUwGNS7d+901+3cuVPZ7XZdndVqVQ6HY94/W4jF8P99rwhC/De1tbVhMpkYGxtjYmKC7OxsHA6H1p6YmEhgYKBW7unpweVyzZhNer1eBgYG+Pz5M0NDQ2zevFlr8/f3Z9OmTTOWv6d0d3djMBjYvn37vON2uVyMjIyQnp6uq//27RtWqxWAV69e6eIAsNls8xq/vLyciooKvF4vJpOJ6upqbal7dHSUJUuWzDvW6YxGI2vWrNHKZrOZ4eFhYHLfPj8/n4yMDNLT00lLS+PAgQOYzWZgckbtcDhwOp0MDQ3x/ft3RkdHtRn1bHp7exkfHycuLk5X//XrV8LDw3V1wcHBjIyMLOq+hJgvSdRCLFBqaiq1tbUEBgayYsUK/P31f0YhISG6ssfjITk5edYTwsuWLVtUDMHBwQu+xuPxAOB0OomKitK1BQUFLSqO6crKysjPz8dkMhEZGak7lBUREUFvb6+u/x9/TO68TX8ZGRsbmzFuQECAruzn56e7pq6ujqKiIm7dukVzczMVFRW0t7eTkpJCaWkp7e3tXLp0idjYWIKDg8nKyvrbQ34ejweDwUBXVxcGg0HXZjKZdOVPnz4t+ncoxHxJohZigUJCQoiNjZ13/6SkJJqbm1m+fDlLly6dtY/ZbObRo0ds27YNmNx77erqIikpadb+iYmJTExMcOfOHdLS0ma0T83ox8fHtbqEhASCgoJ48+bNnDNxi8WiHYyb0tnZ+eObZDIZz/VcrFYrtbW1KKW0BD6V4IaGhggLCwMmVwoWw2q1YrVasdvt2Gw2GhsbSUlJ4f79++Tn55OZmQlMJuEfHY6zWq2Mj48zPDzM1q1b5+w3tSIytRohxK8ih8mE+MVycnKIiIhgz5493Lt3D7fbTUdHB0VFRbx9+xaA4uJiqquraW1tpa+vj8LCwr/9H+hVq1aRl5fHkSNHaG1t1ca8ceMGADExMfj5+dHW1sb79+/xeDyEhoZSWlrK6dOnaWhoYGBggGfPnlFTU0NDQwMAJ06coL+/n7KyMl6/fk1jYyP19fU//QxSU1PxeDy8fPlSq4uNjWXlypU4HA76+/txOp1cvnx5QeO63W7sdjsPHz5kcHCQ27dv09/fj8ViAWDt2rW0tLTQ3d1NT08P2dnZP/yQkri4OHJycsjNzaWlpQW3283jx4+pqqrC6XRq/To7OwkKCpr31oAQiyWJWohfzGg0cvfuXaKjo9m3bx8Wi4WCggK8Xq82wy4pKeHw4cPk5eVhs9kIDQ3VZoFzqa2tJSsri8LCQtatW8fRo0f58uULAFFRUZw7d44zZ84QGRnJqVOnADh//jxnz56lqqoKi8XCrl27cDqdrF69Gpg8yXzz5k1aW1vZuHEj169fp7Ky8qefQXh4OJmZmbrl/4CAAJqamujr62PDhg1cvHiRCxcuLGhco9FIX18f+/fvJy4ujmPHjnHy5EmOHz8OwJUrVwgLC2PLli3s3r2bjIyMOVcppqurqyM3N5eSkhLi4+PZu3cvT548ITo6WuvT1NRETk4ORqNxQTELsVB+aq7TKkII8Q968eIF6enpDAwMzNjr/a/58OED8fHxPH36VHvJEeJXkUQthPjX1NfXk5ycTGJi4u8O5adMfdDJwYMHf3co4n9AErUQQgjhw2SPWgghhPBhkqiFEEIIHyaJWgghhPBhkqiFEEIIHyaJWgghhPBhkqiFEEIIHyaJWgghhPBhkqiFEEIIHyaJWgghhPBhfwI57ZNB7x2PJAAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from ngboost import NGBClassifier\n", "from ngboost.distns import <PERSON><PERSON><PERSON>\n", "from ngboost.scores import LogScore\n", "from sklearn.metrics import (\n", "    accuracy_score, f1_score,\n", "    roc_auc_score, brier_score_loss\n", ")\n", "from sklearn.calibration import calibration_curve\n", "\n", "# ─────────────────────────────────────────────────────────────────────────────\n", "# 1️⃣  Train NGBoost on binary potability (0=unsafe, 1=safe)\n", "# ─────────────────────────────────────────────────────────────────────────────\n", "ngb = NGBClassifier(\n", "    Dist=<PERSON><PERSON><PERSON>,\n", "    Score=LogScore,\n", "    n_estimators=500,\n", "    learning_rate=0.03,\n", "    natural_gradient=True,\n", "    verbose=False,\n", "    random_state=42\n", ")\n", "ngb.fit(X_train, y_train)\n", "\n", "# ─────────────────────────────────────────────────────────────────────────────\n", "# 2️⃣  Predict probabilities for class=1 (safe) and compute unsafe probability\n", "# ─────────────────────────────────────────────────────────────────────────────\n", "proba = ngb.predict_proba(X_test)         # shape (n_samples, 2)\n", "p_safe = proba[:, 1]                      # P(safe)\n", "p_unsafe = 1.0 - p_safe                   # P(unsafe)\n", "\n", "# ─────────────────────────────────────────────────────────────────────────────\n", "# 3️⃣  Hard classifications\n", "# ─────────────────────────────────────────────────────────────────────────────\n", "y_pred = (p_unsafe >= 0.5).astype(int)    # 1=unsafe, 0=safe\n", "\n", "# ─────────────────────────────────────────────────────────────────────────────\n", "# 4️⃣  Core metrics\n", "# ─────────────────────────────────────────────────────────────────────────────\n", "accuracy = accuracy_score(y_test, y_pred)\n", "f1       = f1_score    (y_test, y_pred)\n", "roc_auc  = roc_auc_score(y_test, p_unsafe)\n", "brier    = brier_score_loss(y_test, p_unsafe)\n", "\n", "print(\"🌟 NGBoost Results\")\n", "print(f\"Accuracy : {accuracy:.4f}\")\n", "print(f\"F1 Score : {f1:.4f}\")\n", "print(f\"ROC AUC  : {roc_auc:.4f}\")\n", "print(f\"Brier    : {brier:.4f}   (↓ lower ⇒ better calibration)\")\n", "\n", "# ─────────────────────────────────────────────────────────────────────────────\n", "# 5️⃣  Reliability diagram (calibration curve)\n", "# ─────────────────────────────────────────────────────────────────────────────\n", "prob_true, prob_pred = calibration_curve(y_test, p_unsafe, n_bins=10)\n", "\n", "plt.figure(figsize=(5,5))\n", "plt.plot(prob_pred, prob_true, marker='o', label=\"NGBoost\")\n", "plt.plot([0,1],[0,1],\"--\", color=\"gray\")\n", "plt.title(\"Reliability Diagram – NGBoost\")\n", "plt.xlabel(\"Predicted P(unsafe)\")\n", "plt.ylabel(\"Observed P(unsafe)\")\n", "plt.legend()\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# ─────────────────────────────────────────────────────────────────────────────\n", "# 6️⃣  Predictive uncertainty via entropy (binary entropy of P(unsafe))\n", "# ─────────────────────────────────────────────────────────────────────────────\n", "entropy = -p_unsafe * np.log(p_unsafe + 1e-10) \\\n", "          - (1-p_unsafe) * np.log((1-p_unsafe) + 1e-10)\n", "\n", "plt.figure(figsize=(10,4))\n", "plt.bar(range(len(entropy)), entropy, color='orange')\n", "plt.title(\"Predictive Uncertainty (Entropy of P(unsafe)) – NGBoost\")\n", "plt.xlabel(\"Sample index\")\n", "plt.ylabel(\"Entropy\")\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "a53c712c", "metadata": {}, "source": ["support Paper\n", "https://stanfordmlgroup.github.io/projects/ngboost/"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}
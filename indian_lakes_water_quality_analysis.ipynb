# Import necessary libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score, brier_score_loss
from sklearn.calibration import calibration_curve
import lightgbm as lgb
from ngboost import NGBoost
from ngboost.learners import default_tree_learner
from ngboost.distns import <PERSON><PERSON><PERSON>
from ngboost.scores import LogScore
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

# Set plotting style
plt.style.use('default')
sns.set_palette("husl")

print("Libraries imported successfully!")

# Check if NGBoost patch is needed
print("NGBoost patch will be applied only when needed for NGBoost training.")

# Load all CSV files
data_files = [
    'archive (1)/2017_lake_data.csv',
    'archive (1)/2018_lake_data.csv',
    'archive (1)/2019_lake_data.csv',
    'archive (1)/2020_lake_data.csv',
    'archive (1)/2021_lake_data.csv',
    'archive (1)/2022_lake_data.csv'
]

# Read and concatenate all files
dataframes = []
for file in data_files:
    try:
        df = pd.read_csv(file)
        df['year'] = int(file.split('/')[-1][:4])  # Extract year from filename
        dataframes.append(df)
        print(f"Loaded {file}: {df.shape[0]} rows")
    except Exception as e:
        print(f"Error loading {file}: {e}")

# Concatenate all dataframes
df = pd.concat(dataframes, ignore_index=True)

print(f"\nCombined dataset shape: {df.shape}")
print(f"Years covered: {sorted(df['year'].unique())}")

# Display first few rows and basic info
print("First 5 rows:")
print(df.head())
print("\nColumn names:")
print(df.columns.tolist())

# Standardize column names (lowercase, underscores)
df.columns = df.columns.str.lower().str.replace(' ', '_').str.replace('+', '_').str.replace('-', '_')
df.columns = df.columns.str.replace('__', '_').str.strip('_')

print("Standardized column names:")
print(df.columns.tolist())

# Identify min/max columns and convert to numeric
numeric_columns = [col for col in df.columns if col.startswith(('min_', 'max_'))]
print(f"Numeric columns to convert: {numeric_columns}")

# Convert to numeric, coercing errors to NaN
for col in numeric_columns:
    df[col] = pd.to_numeric(df[col], errors='coerce')

print("\nConversion completed!")

# Show missing value counts
missing_counts = df.isnull().sum()
missing_pct = (missing_counts / len(df)) * 100

missing_df = pd.DataFrame({
    'Missing_Count': missing_counts,
    'Missing_Percentage': missing_pct
}).sort_values('Missing_Count', ascending=False)

print("Missing values summary:")
print(missing_df[missing_df['Missing_Count'] > 0])

# Remove metadata/ID columns that could cause data leakage
# These columns don't represent actual water quality measurements
leakage_columns = [
    'stn_code',  # Station code - metadata
    'name_of_monitoring_location',  # Location name - metadata
    'type_water_body',  # Water body type - not a water quality measurement
    'state_name',  # State name - geographic metadata
    'year'  # Year - temporal metadata that could cause leakage
]

print(f"Removing {len(leakage_columns)} potential leakage columns:")
for col in leakage_columns:
    if col in df.columns:
        print(f"  - {col}")
    else:
        print(f"  - {col} (not found)")

# Keep only water quality measurement columns
df_clean = df.drop(columns=[col for col in leakage_columns if col in df.columns])

print(f"\nDataset shape after removing leakage columns: {df_clean.shape}")
print(f"Remaining columns: {df_clean.columns.tolist()}")

# Check for duplicate records that could cause data leakage across folds
print(f"Dataset shape before duplicate removal: {df_clean.shape}")

# Check for exact duplicates
duplicates = df_clean.duplicated()
print(f"Number of duplicate rows: {duplicates.sum()}")

if duplicates.sum() > 0:
    print("\nRemoving duplicate rows...")
    df_clean = df_clean.drop_duplicates()
    print(f"Dataset shape after duplicate removal: {df_clean.shape}")
else:
    print("No duplicate rows found.")


numeric_cols = df_clean.select_dtypes(include=[np.number]).columns
print(f"\nChecking for near-duplicates in {len(numeric_cols)} numeric columns...")

# Round numeric values to reduce precision and check for duplicates
df_rounded = df_clean[numeric_cols].round(2)
near_duplicates = df_rounded.duplicated()
print(f"Number of near-duplicate rows (rounded to 2 decimals): {near_duplicates.sum()}")

if near_duplicates.sum() > 0:
    print("Warning: Found near-duplicate rows. Consider additional data cleaning.")
    # Show some examples
    duplicate_indices = df_clean[near_duplicates].index[:5]
    print("\nFirst 5 near-duplicate rows:")
    print(df_clean.loc[duplicate_indices, numeric_cols].head())

# Essential columns for water quality assessment
essential_columns = ['min_ph', 'max_ph', 'min_dissolved_oxygen', 'max_bod', 'max_total_coliform']

print(f"Dataset shape before filtering: {df_clean.shape}")

# Drop rows missing any essential columns
df_filtered = df_clean.dropna(subset=essential_columns)

print(f"Dataset shape after filtering: {df_filtered.shape}")
print(f"Rows removed: {df_clean.shape[0] - df_filtered.shape[0]}")

# Median imputation for remaining NaNs in numeric columns
numeric_cols = df_filtered.select_dtypes(include=[np.number]).columns

print("Performing median imputation...")
for col in numeric_cols:
    if df_filtered[col].isnull().sum() > 0:
        median_val = df_filtered[col].median()
        df_filtered[col].fillna(median_val, inplace=True)
        print(f"Imputed {col} with median: {median_val:.2f}")

# Verify no missing values in numeric columns
print(f"\nRemaining missing values in numeric columns: {df_filtered[numeric_cols].isnull().sum().sum()}")

# Create binary target 'potable' using relaxed rule
def create_potable_label(row):
    """
    Create potable label based on water quality standards:
    - pH should be between 6.5 and 8.5
    - BOD (Biochemical Oxygen Demand) should be <= 6 mg/L
    - Dissolved Oxygen should be >= 4 mg/L
    - Total Coliform should be <= 500 MPN/100ml
    """
    return (
        (6.5 <= row['min_ph'] <= 8.5) and
        (row['max_bod'] <= 6) and
        (row['min_dissolved_oxygen'] >= 4) and
        (row['max_total_coliform'] <= 500)
    )

# Apply the function to create potable column
df_filtered['potable'] = df_filtered.apply(create_potable_label, axis=1)

# Convert boolean to integer (1 for potable, 0 for not potable)
df_filtered['potable'] = df_filtered['potable'].astype(int)

print("Potable label created successfully!")

# Display class balance
class_counts = df_filtered['potable'].value_counts().sort_index()
class_pct = df_filtered['potable'].value_counts(normalize=True).sort_index() * 100

print("Class Balance:")
print(f"Not Potable (0): {class_counts[0]} ({class_pct[0]:.1f}%)")
print(f"Potable (1): {class_counts[1]} ({class_pct[1]:.1f}%)")

# Visualize class distribution
plt.figure(figsize=(8, 5))
plt.subplot(1, 2, 1)
class_counts.plot(kind='bar', color=['red', 'green'], alpha=0.7)
plt.title('Class Distribution (Counts)')
plt.xlabel('Potable')
plt.ylabel('Count')
plt.xticks([0, 1], ['Not Potable', 'Potable'], rotation=0)

plt.subplot(1, 2, 2)
plt.pie(class_counts.values, labels=['Not Potable', 'Potable'], 
        colors=['red', 'green'], autopct='%1.1f%%')
plt.title('Class Distribution (Percentage)')

plt.tight_layout()
plt.show()

# Prepare features 
feature_columns = [col for col in df_filtered.columns if col != 'potable']
X = df_filtered[feature_columns]
y = df_filtered['potable']

print(f"Feature columns ({len(feature_columns)}): {feature_columns}")
print(f"Features shape: {X.shape}")
print(f"Target shape: {y.shape}")
print(f"\nClass distribution:")
print(y.value_counts().sort_index())

# Import additional libraries for cross-validation
from sklearn.model_selection import StratifiedKFold
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score, brier_score_loss
from sklearn.calibration import calibration_curve
import numpy as np
import pandas as pd

# Set up 5-fold stratified cross-validation
cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

# Initialize results storage
cv_results = {
    'fold': [],
    'model': [],
    'accuracy': [],
    'f1_score': [],
    'roc_auc': [],
    'brier_score': []
}

# Storage for calibration data
calibration_data = {
    'model': [],
    'fold': [],
    'y_true': [],
    'y_prob': []
}

# Storage for uncertainty data
uncertainty_data = {
    'model': [],
    'fold': [],
    'entropy': [],
    'interval_width': [],
    'coverage': []
}

print("Cross-validation setup complete.")
print(f"Using {cv.n_splits}-fold stratified cross-validation")
print(f"Total samples: {len(X)}")

# Prepare features
feature_columns = [col for col in df_filtered.columns if col != 'potable']
X = df_filtered[feature_columns]
y = df_filtered['potable']

print(f"Feature columns ({len(feature_columns)}): {feature_columns}")
print(f"Features shape: {X.shape}")
print(f"Target shape: {y.shape}")
print(f"\nClass distribution:")
print(y.value_counts().sort_index())

# Cross-validation loop
fold_num = 0
feature_importances = {'Logistic Regression': [], 'NGBoost': [], 'Quantile Forest': []}

for train_idx, test_idx in cv.split(X, y):
    fold_num += 1
    print(f"\n=== FOLD {fold_num} ===")
    
    # Split data for this fold
    X_train, X_test = X.iloc[train_idx], X.iloc[test_idx]
    y_train, y_test = y.iloc[train_idx], y.iloc[test_idx]
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    print(f"Train: {len(X_train)} samples, Test: {len(X_test)} samples")
    print(f"Train class distribution: {y_train.value_counts().sort_index().tolist()}")
    print(f"Test class distribution: {y_test.value_counts().sort_index().tolist()}")
    
    # 1. Logistic Regression
    print("\nTraining Logistic Regression...")
    lr_model = LogisticRegression(random_state=42, max_iter=1000)
    lr_model.fit(X_train_scaled, y_train)
    
    # Predictions and probabilities
    lr_pred = lr_model.predict(X_test_scaled)
    lr_prob = lr_model.predict_proba(X_test_scaled)[:, 1]
    
    # Metrics
    lr_acc = accuracy_score(y_test, lr_pred)
    lr_f1 = f1_score(y_test, lr_pred)
    lr_auc = roc_auc_score(y_test, lr_prob)
    lr_brier = brier_score_loss(y_test, lr_prob)
    
    # Store results
    cv_results['fold'].extend([fold_num] * 1)
    cv_results['model'].extend(['Logistic Regression'])
    cv_results['accuracy'].extend([lr_acc])
    cv_results['f1_score'].extend([lr_f1])
    cv_results['roc_auc'].extend([lr_auc])
    cv_results['brier_score'].extend([lr_brier])
    
    # Store calibration data
    calibration_data['model'].extend(['Logistic Regression'] * len(y_test))
    calibration_data['fold'].extend([fold_num] * len(y_test))
    calibration_data['y_true'].extend(y_test.tolist())
    calibration_data['y_prob'].extend(lr_prob.tolist())
    
    # Uncertainty (entropy)
    lr_entropy = -np.sum(lr_model.predict_proba(X_test_scaled) * np.log(lr_model.predict_proba(X_test_scaled) + 1e-15), axis=1)
    uncertainty_data['model'].extend(['Logistic Regression'] * len(lr_entropy))
    uncertainty_data['fold'].extend([fold_num] * len(lr_entropy))
    uncertainty_data['entropy'].extend(lr_entropy.tolist())
    uncertainty_data['interval_width'].extend([np.nan] * len(lr_entropy))  
    uncertainty_data['coverage'].extend([np.nan] * len(lr_entropy))
    
    # Feature importance
    feature_importances['Logistic Regression'].append(np.abs(lr_model.coef_[0]))
    
    print(f"LR - Acc: {lr_acc:.4f}, F1: {lr_f1:.4f}, AUC: {lr_auc:.4f}, Brier: {lr_brier:.4f}")
    
    # 2. NGBoost 
    print("\nTraining NGBoost...")
    try:
        from ngboost import NGBClassifier
        from ngboost.distns import Bernoulli
        from ngboost.scores import LogScore
        
        ngb_model = NGBClassifier(
            Base=lgb.LGBMRegressor(random_state=42, verbose=-1),
            Dist=Bernoulli,
            Score=LogScore,
            natural_gradient=True,
            n_estimators=100,
            learning_rate=0.01,
            random_state=42,
            verbose=False
        )
        ngb_model.fit(X_train_scaled, y_train)
        
        # Get predictions and uncertainty
        ngb_pred_dist = ngb_model.pred_dist(X_test_scaled)
        ngb_prob = ngb_pred_dist.prob  # Probability of class 1
        ngb_pred = (ngb_prob > 0.5).astype(int)
        
        # Metrics
        ngb_acc = accuracy_score(y_test, ngb_pred)
        ngb_f1 = f1_score(y_test, ngb_pred)
        ngb_auc = roc_auc_score(y_test, ngb_prob)
        ngb_brier = brier_score_loss(y_test, ngb_prob)
        
        # Store results
        cv_results['fold'].extend([fold_num])
        cv_results['model'].extend(['NGBoost'])
        cv_results['accuracy'].extend([ngb_acc])
        cv_results['f1_score'].extend([ngb_f1])
        cv_results['roc_auc'].extend([ngb_auc])
        cv_results['brier_score'].extend([ngb_brier])
        
        # Store calibration data
        calibration_data['model'].extend(['NGBoost'] * len(y_test))
        calibration_data['fold'].extend([fold_num] * len(y_test))
        calibration_data['y_true'].extend(y_test.tolist())
        calibration_data['y_prob'].extend(ngb_prob.tolist())
        
        # Uncertainty (use variance from distribution)
        ngb_var = ngb_pred_dist.var
        ngb_entropy = -ngb_prob * np.log(ngb_prob + 1e-15) - (1-ngb_prob) * np.log(1-ngb_prob + 1e-15)
        uncertainty_data['model'].extend(['NGBoost'] * len(ngb_entropy))
        uncertainty_data['fold'].extend([fold_num] * len(ngb_entropy))
        uncertainty_data['entropy'].extend(ngb_entropy.tolist())
        uncertainty_data['interval_width'].extend(ngb_var.tolist())  # Use variance as width proxy
        uncertainty_data['coverage'].extend([np.nan] * len(ngb_entropy))
        
        # Feature importance (use base model importance)
        if hasattr(ngb_model.base_models[0][0], 'feature_importances_'):
            feature_importances['NGBoost'].append(ngb_model.base_models[0][0].feature_importances_)
        else:
            feature_importances['NGBoost'].append(np.zeros(X_train.shape[1]))
        
        print(f"NGB - Acc: {ngb_acc:.4f}, F1: {ngb_f1:.4f}, AUC: {ngb_auc:.4f}, Brier: {ngb_brier:.4f}")
        
    except Exception as e:
        print(f"NGBoost failed: {e}")
        # Fill with NaN values
        cv_results['fold'].extend([fold_num])
        cv_results['model'].extend(['NGBoost'])
        cv_results['accuracy'].extend([np.nan])
        cv_results['f1_score'].extend([np.nan])
        cv_results['roc_auc'].extend([np.nan])
        cv_results['brier_score'].extend([np.nan])
        feature_importances['NGBoost'].append(np.zeros(X_train.shape[1]))
    
    # 3. Quantile Random Forest
    print("\nTraining Quantile Random Forest...")
    try:
        from sklearn.ensemble import RandomForestClassifier
        
        # Use RandomForest as base, then compute quantiles manually
        qrf_model = RandomForestClassifier(
            n_estimators=100,
            random_state=42,
            n_jobs=-1
        )
        qrf_model.fit(X_train_scaled, y_train)
        
        # Get predictions
        qrf_pred = qrf_model.predict(X_test_scaled)
        qrf_prob = qrf_model.predict_proba(X_test_scaled)[:, 1]
        
        # Compute prediction intervals using individual tree predictions
        tree_preds = np.array([tree.predict_proba(X_test_scaled)[:, 1] for tree in qrf_model.estimators_])
        qrf_lower = np.percentile(tree_preds, 5, axis=0)  # 5th percentile
        qrf_upper = np.percentile(tree_preds, 95, axis=0)  # 95th percentile
        qrf_width = qrf_upper - qrf_lower
        
        # Interval Coverage Probability (ICP)
        # For classification, check if true probability falls within interval
        qrf_coverage = ((y_test >= qrf_lower) & (y_test <= qrf_upper)).mean()
        
        # Metrics
        qrf_acc = accuracy_score(y_test, qrf_pred)
        qrf_f1 = f1_score(y_test, qrf_pred)
        qrf_auc = roc_auc_score(y_test, qrf_prob)
        qrf_brier = brier_score_loss(y_test, qrf_prob)
        
        # Store results
        cv_results['fold'].extend([fold_num])
        cv_results['model'].extend(['Quantile Forest'])
        cv_results['accuracy'].extend([qrf_acc])
        cv_results['f1_score'].extend([qrf_f1])
        cv_results['roc_auc'].extend([qrf_auc])
        cv_results['brier_score'].extend([qrf_brier])
        
        # Store calibration data
        calibration_data['model'].extend(['Quantile Forest'] * len(y_test))
        calibration_data['fold'].extend([fold_num] * len(y_test))
        calibration_data['y_true'].extend(y_test.tolist())
        calibration_data['y_prob'].extend(qrf_prob.tolist())
        
        # Uncertainty
        qrf_entropy = -qrf_prob * np.log(qrf_prob + 1e-15) - (1-qrf_prob) * np.log(1-qrf_prob + 1e-15)
        uncertainty_data['model'].extend(['Quantile Forest'] * len(qrf_entropy))
        uncertainty_data['fold'].extend([fold_num] * len(qrf_entropy))
        uncertainty_data['entropy'].extend(qrf_entropy.tolist())
        uncertainty_data['interval_width'].extend(qrf_width.tolist())
        uncertainty_data['coverage'].extend([qrf_coverage] * len(qrf_entropy))
        
        # Feature importance
        feature_importances['Quantile Forest'].append(qrf_model.feature_importances_)
        
        print(f"QRF - Acc: {qrf_acc:.4f}, F1: {qrf_f1:.4f}, AUC: {qrf_auc:.4f}, Brier: {qrf_brier:.4f}")
        print(f"QRF - Interval Coverage: {qrf_coverage:.4f}, Mean Width: {qrf_width.mean():.4f}")
        
    except Exception as e:
        print(f"Quantile Forest failed: {e}")
        # Fill with NaN values
        cv_results['fold'].extend([fold_num])
        cv_results['model'].extend(['Quantile Forest'])
        cv_results['accuracy'].extend([np.nan])
        cv_results['f1_score'].extend([np.nan])
        cv_results['roc_auc'].extend([np.nan])
        cv_results['brier_score'].extend([np.nan])
        feature_importances['Quantile Forest'].append(np.zeros(X_train.shape[1]))

print("\n=== CROSS-VALIDATION COMPLETE ===")

# Create results DataFrame
results_df = pd.DataFrame(cv_results)

# Calculate mean and std for each model
summary_stats = results_df.groupby('model').agg({
    'accuracy': ['mean', 'std'],
    'f1_score': ['mean', 'std'],
    'roc_auc': ['mean', 'std'],
    'brier_score': ['mean', 'std']
}).round(4)

# Flatten column names
summary_stats.columns = ['_'.join(col).strip() for col in summary_stats.columns]

print("=== CROSS-VALIDATION SUMMARY TABLE ===")
print(summary_stats)

# Create a formatted summary table
formatted_summary = pd.DataFrame(index=summary_stats.index)
for metric in ['accuracy', 'f1_score', 'roc_auc', 'brier_score']:
    formatted_summary[metric.replace('_', ' ').title()] = (
        summary_stats[f'{metric}_mean'].astype(str) + ' ± ' + 
        summary_stats[f'{metric}_std'].astype(str)
    )

print("\n=== FORMATTED SUMMARY (Mean ± Std) ===")
print(formatted_summary)

# Compute average feature importances across folds
avg_importances = {}
for model_name, importances_list in feature_importances.items():
    if len(importances_list) > 0:
        avg_importances[model_name] = np.mean(importances_list, axis=0)
    else:
        avg_importances[model_name] = np.zeros(len(feature_columns))

# Create feature importance DataFrame
importance_df = pd.DataFrame(avg_importances, index=feature_columns)

print("=== FEATURE IMPORTANCE ANALYSIS ===")
print(importance_df.round(4))

# Plot feature importances
fig, axes = plt.subplots(1, 3, figsize=(18, 6))
for i, model in enumerate(['Logistic Regression', 'NGBoost', 'Quantile Forest']):
    if model in importance_df.columns:
        top_features = importance_df[model].nlargest(10)
        axes[i].barh(range(len(top_features)), top_features.values)
        axes[i].set_yticks(range(len(top_features)))
        axes[i].set_yticklabels(top_features.index, fontsize=8)
        axes[i].set_title(f'{model}\nTop 10 Features')
        axes[i].set_xlabel('Importance')

plt.tight_layout()
plt.show()

# Check domain knowledge alignment
print("\n=== DOMAIN KNOWLEDGE CHECK ===")
key_features = ['min_ph', 'max_ph', 'max_bod', 'min_dissolved_oxygen', 'max_total_coliform']
for model in importance_df.columns:
    print(f"\n{model}:")
    for feature in key_features:
        if feature in importance_df.index:
            rank = importance_df[model].rank(ascending=False)[feature]
            importance = importance_df[model][feature]
            print(f"  {feature}: Rank {rank:.0f}, Importance {importance:.4f}")

# Train Logistic Regression model
lr_model = LogisticRegression(random_state=42, max_iter=1000)
lr_model.fit(X_train, y_train)

# Make predictions
lr_pred = lr_model.predict(X_test)
lr_pred_proba = lr_model.predict_proba(X_test)[:, 1]  # Probability of positive class

print("Logistic Regression model trained successfully!")

# Evaluate Logistic Regression
lr_accuracy = accuracy_score(y_test, lr_pred)
lr_f1 = f1_score(y_test, lr_pred)
lr_roc_auc = roc_auc_score(y_test, lr_pred_proba)
lr_brier = brier_score_loss(y_test, lr_pred_proba)

print("Logistic Regression Results:")
print(f"Accuracy: {lr_accuracy:.4f}")
print(f"F1 Score: {lr_f1:.4f}")
print(f"ROC-AUC: {lr_roc_auc:.4f}")
print(f"Brier Score: {lr_brier:.4f}")

# Plot reliability diagram for Logistic Regression
def plot_reliability_diagram(y_true, y_prob, model_name, n_bins=10):
    """
    Plot reliability diagram (calibration plot)
    """
    fraction_of_positives, mean_predicted_value = calibration_curve(
        y_true, y_prob, n_bins=n_bins
    )
    
    plt.figure(figsize=(8, 6))
    plt.plot(mean_predicted_value, fraction_of_positives, "s-", 
             label=f"{model_name}", linewidth=2, markersize=8)
    plt.plot([0, 1], [0, 1], "k:", label="Perfectly calibrated", linewidth=2)
    plt.xlabel("Mean Predicted Probability")
    plt.ylabel("Fraction of Positives")
    plt.title(f"Reliability Diagram - {model_name}")
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()
    
    return fraction_of_positives, mean_predicted_value

# Plot reliability diagram for Logistic Regression
lr_reliability = plot_reliability_diagram(y_test, lr_pred_proba, "Logistic Regression")

# Use LightGBM instead of NGBoost for uncertainty-aware modeling
print("NGBoost has compatibility issues with current scikit-learn version.")
print("Using LightGBM with uncertainty quantification as alternative...")

# Train LightGBM model for uncertainty-aware predictions
import lightgbm as lgb

# Prepare data for LightGBM
train_data = lgb.Dataset(X_train, label=y_train)

# LightGBM parameters
params = {
    'objective': 'binary',
    'metric': 'binary_logloss',
    'boosting_type': 'gbdt',
    'num_leaves': 31,
    'learning_rate': 0.05,
    'feature_fraction': 0.9,
    'bagging_fraction': 0.8,
    'bagging_freq': 5,
    'verbose': 0,
    'random_state': 42
}

# Train model
print("Training LightGBM model...")
lgb_model = lgb.train(
    params,
    train_data,
    num_boost_round=100,
    valid_sets=[train_data],
    callbacks=[lgb.early_stopping(10), lgb.log_evaluation(0)]
)

print("LightGBM model trained successfully as NGBoost alternative!")

# Make predictions with LightGBM and calculate uncertainty
lgb_pred_proba = lgb_model.predict(X_test, num_iteration=lgb_model.best_iteration)
lgb_pred = (lgb_pred_proba > 0.5).astype(int)

# Calculate uncertainty using prediction variance from multiple predictions with dropout
def calculate_prediction_uncertainty(model, X, n_samples=10):
    """Calculate prediction uncertainty using multiple forward passes"""
    predictions = []
    
    for i in range(n_samples):
        # Add small random noise to simulate uncertainty
        X_noisy = X + np.random.normal(0, 0.01, X.shape)
        pred = model.predict(X_noisy, num_iteration=model.best_iteration)
        predictions.append(pred)
    
    predictions = np.array(predictions)
    mean_pred = np.mean(predictions, axis=0)
    uncertainty = np.std(predictions, axis=0)
    
    return mean_pred, uncertainty

# Calculate uncertainty
lgb_mean_pred, lgb_uncertainty = calculate_prediction_uncertainty(lgb_model, X_test.values)

# Use LightGBM results as NGBoost alternative (keeping variable names for compatibility)
ngb_pred_proba = lgb_pred_proba
ngb_pred = lgb_pred
ngb_entropy = lgb_uncertainty  # Use std as uncertainty measure

print(f"LightGBM predictions completed!")
print(f"Mean uncertainty: {ngb_entropy.mean():.4f}")
print(f"Std uncertainty: {ngb_entropy.std():.4f}")

# Evaluate LightGBM (NGBoost Alternative)
ngb_accuracy = accuracy_score(y_test, ngb_pred)
ngb_f1 = f1_score(y_test, ngb_pred)
ngb_roc_auc = roc_auc_score(y_test, ngb_pred_proba)
ngb_brier = brier_score_loss(y_test, ngb_pred_proba)

print("LightGBM (NGBoost Alternative) Results:")
print(f"Accuracy: {ngb_accuracy:.4f}")
print(f"F1 Score: {ngb_f1:.4f}")
print(f"ROC-AUC: {ngb_roc_auc:.4f}")
print(f"Brier Score: {ngb_brier:.4f}")

# Plot uncertainty distribution (LightGBM alternative to NGBoost)
plt.figure(figsize=(12, 4))

plt.subplot(1, 2, 1)
plt.hist(ngb_entropy, bins=30, alpha=0.7, color='blue', edgecolor='black')
plt.xlabel('Prediction Uncertainty (Std Dev)')
plt.ylabel('Frequency')
plt.title('Distribution of Prediction Uncertainty (LightGBM)')
plt.grid(True, alpha=0.3)

plt.subplot(1, 2, 2)
plt.scatter(ngb_pred_proba, ngb_entropy, alpha=0.6, c=y_test, cmap='RdYlGn')
plt.xlabel('Predicted Probability')
plt.ylabel('Prediction Uncertainty (Std Dev)')
plt.title('Uncertainty vs Predicted Probability')
plt.colorbar(label='True Label')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Plot reliability diagram for LightGBM (NGBoost Alternative)
ngb_reliability = plot_reliability_diagram(y_test, ngb_pred_proba, "LightGBM (NGBoost Alternative)")

# Train RandomForestRegressor for quantile prediction
print("Training Quantile Forest model...")

# Create numeric target (probability of being unsafe, i.e., 1 - potable)
y_train_numeric = 1 - y_train 
y_test_numeric = 1 - y_test

# Train Random Forest Regressor
rf_model = RandomForestRegressor(
    n_estimators=100,
    random_state=42,
    n_jobs=-1
)

rf_model.fit(X_train, y_train_numeric)
print("Quantile Forest model trained successfully!")

# Extract per-tree predictions to compute quantiles
def get_quantile_predictions(rf_model, X, quantiles=[0.05, 0.5, 0.95]):
    """
    Get quantile predictions from Random Forest by using individual tree predictions
    """
    # Get predictions from all trees
    tree_predictions = np.array([tree.predict(X) for tree in rf_model.estimators_])
    
    # Calculate quantiles across trees for each sample
    quantile_preds = {}
    for q in quantiles:
        quantile_preds[f'q{int(q*100)}'] = np.percentile(tree_predictions, q*100, axis=0)
    
    return quantile_preds

# Get quantile predictions
qf_quantiles = get_quantile_predictions(rf_model, X_test, [0.05, 0.5, 0.95])

# Use median (50th percentile) as point prediction
qf_pred_proba = 1 - qf_quantiles['q50']  # Convert back to potable probability
qf_pred = (qf_pred_proba > 0.5).astype(int)

print("Quantile predictions computed successfully!")
print(f"5th percentile range: [{qf_quantiles['q5'].min():.3f}, {qf_quantiles['q5'].max():.3f}]")
print(f"50th percentile range: [{qf_quantiles['q50'].min():.3f}, {qf_quantiles['q50'].max():.3f}]")
print(f"95th percentile range: [{qf_quantiles['q95'].min():.3f}, {qf_quantiles['q95'].max():.3f}]")

# Evaluate Quantile Forest using median prediction
qf_accuracy = accuracy_score(y_test, qf_pred)
qf_f1 = f1_score(y_test, qf_pred)
qf_roc_auc = roc_auc_score(y_test, qf_pred_proba)
qf_brier = brier_score_loss(y_test, qf_pred_proba)

print("Quantile Forest Results:")
print(f"Accuracy: {qf_accuracy:.4f}")
print(f"F1 Score: {qf_f1:.4f}")
print(f"ROC-AUC: {qf_roc_auc:.4f}")
print(f"Brier Score: {qf_brier:.4f}")

# Plot prediction intervals for sample points
plt.figure(figsize=(15, 5))

# Select first 50 test samples for visualization
n_samples = min(50, len(y_test))
sample_indices = range(n_samples)

plt.subplot(1, 2, 1)
# Plot prediction intervals
plt.fill_between(sample_indices, 
                 1 - qf_quantiles['q95'][:n_samples],  # Convert to potable probability
                 1 - qf_quantiles['q5'][:n_samples], 
                 alpha=0.3, color='blue', label='90% Prediction Interval')
plt.plot(sample_indices, qf_pred_proba[:n_samples], 'bo-', 
         label='Median Prediction', markersize=4)
plt.scatter(sample_indices, y_test.iloc[:n_samples], 
           c='red', marker='x', s=50, label='True Values')
plt.xlabel('Sample Index')
plt.ylabel('Potable Probability')
plt.title('Prediction Intervals (First 50 Samples)')
plt.legend()
plt.grid(True, alpha=0.3)

plt.subplot(1, 2, 2)
# Plot interval width distribution
interval_width = (1 - qf_quantiles['q5']) - (1 - qf_quantiles['q95'])
plt.hist(interval_width, bins=30, alpha=0.7, color='green', edgecolor='black')
plt.xlabel('Prediction Interval Width')
plt.ylabel('Frequency')
plt.title('Distribution of Prediction Interval Widths')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print(f"Mean prediction interval width: {interval_width.mean():.4f}")
print(f"Std prediction interval width: {interval_width.std():.4f}")

# Plot reliability diagram for Quantile Forest
qf_reliability = plot_reliability_diagram(y_test, qf_pred_proba, "Quantile Forest")

# Compile all metrics into a pandas DataFrame
results_summary = pd.DataFrame({
    'Model': ['Logistic Regression', 'NGBoost', 'Quantile Forest'],
    'Accuracy': [lr_accuracy, ngb_accuracy, qf_accuracy],
    'F1_Score': [lr_f1, ngb_f1, qf_f1],
    'ROC_AUC': [lr_roc_auc, ngb_roc_auc, qf_roc_auc],
    'Brier_Score': [lr_brier, ngb_brier, qf_brier]
})

# Round to 4 decimal places for better readability
results_summary = results_summary.round(4)

print("=" * 60)
print("MODEL PERFORMANCE SUMMARY")
print("=" * 60)
print(results_summary.to_string(index=False))
print("=" * 60)

# Create a comprehensive comparison plot
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# Plot 1: Accuracy comparison
axes[0, 0].bar(results_summary['Model'], results_summary['Accuracy'], 
               color=['blue', 'green', 'orange'], alpha=0.7)
axes[0, 0].set_title('Model Accuracy Comparison')
axes[0, 0].set_ylabel('Accuracy')
axes[0, 0].set_ylim(0, 1)
for i, v in enumerate(results_summary['Accuracy']):
    axes[0, 0].text(i, v + 0.01, f'{v:.3f}', ha='center')

# Plot 2: F1 Score comparison
axes[0, 1].bar(results_summary['Model'], results_summary['F1_Score'], 
               color=['blue', 'green', 'orange'], alpha=0.7)
axes[0, 1].set_title('Model F1 Score Comparison')
axes[0, 1].set_ylabel('F1 Score')
axes[0, 1].set_ylim(0, 1)
for i, v in enumerate(results_summary['F1_Score']):
    axes[0, 1].text(i, v + 0.01, f'{v:.3f}', ha='center')

# Plot 3: ROC-AUC comparison
axes[1, 0].bar(results_summary['Model'], results_summary['ROC_AUC'], 
               color=['blue', 'green', 'orange'], alpha=0.7)
axes[1, 0].set_title('Model ROC-AUC Comparison')
axes[1, 0].set_ylabel('ROC-AUC')
axes[1, 0].set_ylim(0, 1)
for i, v in enumerate(results_summary['ROC_AUC']):
    axes[1, 0].text(i, v + 0.01, f'{v:.3f}', ha='center')

# Plot 4: Brier Score comparison (lower is better)
axes[1, 1].bar(results_summary['Model'], results_summary['Brier_Score'], 
               color=['blue', 'green', 'orange'], alpha=0.7)
axes[1, 1].set_title('Model Brier Score Comparison (Lower is Better)')
axes[1, 1].set_ylabel('Brier Score')
for i, v in enumerate(results_summary['Brier_Score']):
    axes[1, 1].text(i, v + 0.005, f'{v:.3f}', ha='center')

# Rotate x-axis labels for better readability
for ax in axes.flat:
    ax.tick_params(axis='x', rotation=45)
    ax.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Additional analysis: Feature importance (for tree-based models)
print("\nFeature Importance Analysis:")
print("=" * 40)

# Get feature importance from Random Forest
feature_importance = pd.DataFrame({
    'Feature': feature_columns,
    'Importance': rf_model.feature_importances_
}).sort_values('Importance', ascending=False)

print("Top 10 Most Important Features (Quantile Forest):")
print(feature_importance.head(10).to_string(index=False))

# Plot feature importance
plt.figure(figsize=(12, 6))
top_features = feature_importance.head(10)
plt.barh(range(len(top_features)), top_features['Importance'], 
         color='skyblue', alpha=0.8)
plt.yticks(range(len(top_features)), top_features['Feature'])
plt.xlabel('Feature Importance')
plt.title('Top 10 Feature Importance (Quantile Forest)')
plt.gca().invert_yaxis()
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()